import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import * as _ from 'lodash';
import { InjectRepository } from '@nestjs/typeorm';
import {
  In,
  IsNull,
  <PERSON>Than,
  MoreThanOrEqual,
  Not,
  Repository,
} from 'typeorm';
import { Decimal } from 'decimal.js';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Invoice } from './invoice.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { InvoicePaymentMethod } from './invoice-payment-method.entity';
import { CreditHistory } from '../credit-history/credit-history.entity';
import { Credit } from '../credit/credit.entity';
import { CommissionLogs } from '../employee/commission/commission-logs.entity';
import {
  AppointmentStatus,
  CouponType,
  CreditStatus,
  CreditType,
  InvoiceStatus,
  PaymentMethod,
  PeriodUnit,
  ProductType,
  RecordStatus,
} from 'src/core/enums/entity';
import { Appointment } from '../appointment/appointment.entity';
import { Order } from '../order/order.entity';
import * as moment from 'moment';
import { CreditSetting } from '../settings/credit/credit-setting.entity';
import { CouponItem } from '../inventory/coupon-item.entity';
import { InvoiceCoupon } from './invoice-coupon.entity';
import { BackDateDto, InvoiceQueryDto } from './dto/invoice.dto';
import {
  countExpiryDate,
  getCustomPaginationLimit,
} from '../../core/common/common.utils';
import { IssueCoupon } from '../inventory/issue-coupon.entity';
import handlebars from 'handlebars';
import { MailerService } from '../mailer/mailer.service';
import { v4 as uuidv4 } from 'uuid';
import { SEND_COUPON_CODE } from '../mailer/templates/mjml/constant';
import { renderMJML } from '../mailer/templates/mjml';
import { AuditTrailOperation } from '../audit-trail/audit-trail.entity';
import { AuditTrailService } from '../audit-trail/audit-trail.service';
import { Branch } from '../branch/branch.entity';
import { Customer } from '../customer/customer.entity';
import { MembershipHistory } from '../credit-history/membership-history.entity';
import { Setting } from '../setting/setting.entity';
import { SalesService } from '../sales/sales.service';

@Injectable()
export class InvoiceService extends BaseCrudService<Invoice> {
  constructor(
    @InjectRepository(Invoice) repo: Repository<Invoice>,
    @InjectRepository(Credit) private repoCredit: Repository<Credit>,
    @InjectRepository(CreditSetting)
    private creditSettingRepo: Repository<CreditSetting>,
    @InjectRepository(Order)
    private orderRepo: Repository<Order>,
    private readonly mailerService: MailerService,
    private readonly auditTrailService: AuditTrailService,
    private readonly salesService: SalesService,
  ) {
    super(repo);
  }

  async generateInvoiceNumber(branch: Branch) {
    let count = await this.repo.manager.count(Invoice, {
      where: {
        branch: {
          id: branch.id,
        },
      },
    });
    count = 10000000 + count + 1;
    return count.toString().padStart(8, '0');
  }

  async createOneInvoice(
    crudRequest: CrudRequest,
    dto: any,
    req: Request,
  ): Promise<Invoice> {
    dto.date = new Date();
    dto.id = uuidv4();
    if (!dto.referral) {
      dto.referral = req['user']['id'];
    }
    if (dto?.appointment?.id == undefined) {
      throw new BadRequestException('Appointment is required');
    }
    const appointment = await this.repo.manager.findOne(Appointment, {
      where: { id: dto.appointment.id },
      relations: ['customer', 'invoices', 'invoices.invoicePayments', 'branch'],
    });
    if (!appointment) {
      throw new BadRequestException(
        `Not found Appointment by appointment id ${dto.appointment.id}`,
      );
    }
    dto.code = await this.generateInvoiceNumber(appointment.branch);
    dto.customer = appointment?.customer?.id;
    let sumPaid = new Decimal(0);
    if (dto?.invoicePayments?.length > 0) {
      for (const payment of dto.invoicePayments) {
        if (payment.paymentMethod?.code.toLowerCase() === PaymentMethod.CASH) {
          payment.paid = new Decimal(payment.paid).plus(
            new Decimal(payment?.roundNumber ?? 0),
          );
        }
        sumPaid = sumPaid.plus(new Decimal(payment.paid));
      }
    }
    const calTax = await this.processCalculateTax(
      dto,
      appointment.branch,
      null,
    );
    let total = new Decimal(calTax ? calTax.totalPaid : 0);
    const totalBeforeTax = new Decimal(calTax ? calTax.totalBeforeTax : 0);
    const subTotal = new Decimal(calTax ? calTax.subTotal : 0);
    let sumDiscount = new Decimal(0);
    if (dto.discounts) {
      for (const i of dto.discounts) {
        if (i.discountMoney) {
          sumDiscount = sumDiscount.plus(new Decimal(i.discountMoney));
        }
      }
    }
    if (total.lessThan(0)) {
      throw new BadRequestException(`The amount on the invoice is invalid`);
    }
    const childInvoices = [];
    if (
      appointment?.invoices?.length > 0 &&
      dto?.childInvoices &&
      dto?.childInvoices?.length
    ) {
      for (const invoice of appointment.invoices) {
        if (invoice.status === InvoiceStatus.PART_PAID) {
          total = total.plus(new Decimal(invoice.unPaid));
          childInvoices.push(invoice);
        }
      }
    }
    if (!sumPaid.equals(total) && dto.status === InvoiceStatus.PAID) {
      throw new BadRequestException(
        `The amount paid is invalid. The amount should be ${total}`,
      );
    }
    if (sumPaid.greaterThan(total) && dto.status === InvoiceStatus.PART_PAID) {
      throw new BadRequestException(
        `The amount paid is invalid. The amount should be ${total}`,
      );
    }
    let invoice;
    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      if (dto?.orders?.length) {
        invoice = await queryRunner.manager.save(Invoice, dto);
        if (calTax && calTax.invoiceDiscountIds.length) {
          await queryRunner.manager.save(Invoice, {
            ...invoice,
            branch: appointment.branch,
            invoiceCoupon: calTax.invoiceDiscountIds,
          });
        }
      } else {
        invoice = await appointment.invoices.find(
          (invoice) => invoice.status === InvoiceStatus.PART_PAID,
        );
      }
      //Create audit trail log
      if (invoice.status !== InvoiceStatus.PART_PAID && calTax) {
        await this.auditTrailService.createRecordLog(queryRunner, {
          createdById: req?.['user']?.['id'],
          branchId: appointment.branch?.id,
          action: AuditTrailOperation.CHECKOUT,
          payload: [],
          startTime: req?.['headers']?.['starttime'],
          newPayload: calTax.orderItems || [],
          invoiceId: invoice.id,
        });
      }
      // handle invoice payments
      const invoicePaymentIds = [];
      for (const payment of dto?.invoicePayments || []) {
        const invoicePayment = await queryRunner.manager.save(
          InvoicePaymentMethod,
          {
            paymentMethod: {
              id: payment.paymentMethod.id,
            },
            paid: parseFloat(payment.paid.toFixed(2)),
            branch: dto?.branch?.id ?? null,
            billCode: payment?.billCode ?? null,
            roundNumber: parseFloat(payment?.roundNumber.toFixed(2)) ?? 0,
          },
        );
        invoicePaymentIds.push(invoicePayment.id);
      }
      const invoicePaymentIdArr = invoicePaymentIds.map((id) => {
        return { id };
      });
      if (!dto?.orders?.length) {
        const oldInvoicePaymentIds = invoice.invoicePayments.map(
          (paymentMethod) => ({ id: paymentMethod.id }),
        );
        invoicePaymentIdArr.push(...oldInvoicePaymentIds);
      }
      await queryRunner.manager.getRepository(Invoice).save({
        ...invoice,
        invoicePayments: invoicePaymentIdArr,
        childInvoices: childInvoices.filter((i) => i.code !== invoice.code),
      });
      // End: handle invoice payments

      // get data new invoice
      const updatedInvoice = await queryRunner.manager.findOne(Invoice, {
        where: {
          id: invoice.id,
        },
        relations: [
          'branch',
          'referral',
          'appointment',
          'parentInvoice',
          'childInvoices',
          'childInvoices.orders',
          'childInvoices.orders.items',
          'childInvoices.orders.items.employees',
          'childInvoices.orders.items.product',
          'customer',
          'customer.credits',
          'customer.credits.creditSetting',
          'invoicePayments',
          'invoicePayments.paymentMethod',
          'orders',
          'orders.items',
          'orders.items.employees',
          'orders.items.product',
          'orders.items.product.category',
        ],
      });
      let clonedInvoiceObj = JSON.parse(JSON.stringify(updatedInvoice));
      // calculate total and totalBeforeTax when order empty and order not empty
      let status;
      if (updatedInvoice?.orders?.length > 0 && dto?.orders?.length) {
        const unPaid = new Decimal(total.minus(sumPaid));
        status = unPaid.equals(0)
          ? InvoiceStatus.PAID
          : InvoiceStatus.PART_PAID;
        await queryRunner.manager.save(Invoice, {
          id: updatedInvoice.id,
          total: parseFloat(total.toFixed(2)),
          paid: parseFloat(sumPaid.toFixed(2)),
          unPaid: parseFloat(unPaid.toFixed(2)),
          totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
          subTotal: parseFloat(subTotal.toFixed(2)),
          discount: parseFloat(sumDiscount.toFixed(2)),
          status,
        });
        clonedInvoiceObj = {
          ...clonedInvoiceObj,
          total: parseFloat(total.toFixed(2)),
          paid: parseFloat(sumPaid.toFixed(2)),
          unPaid: parseFloat(unPaid.toFixed(2)),
          totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
          subTotal: parseFloat(subTotal.toFixed(2)),
          discount: parseFloat(sumDiscount.toFixed(2)),
          status,
        };
        // update status for order
        await queryRunner.manager.update(
          Order,
          { invoice: { id: updatedInvoice.id } },
          { status },
        );
        if (status === InvoiceStatus.PART_PAID) {
          await queryRunner.manager.save(Appointment, {
            id: appointment.id,
            status: AppointmentStatus.BOOKING,
          });
        }
      } else {
        const unPaid = new Decimal(
          sumPaid.minus(new Decimal(updatedInvoice.unPaid)),
        );
        const newPaid = new Decimal(
          sumPaid.plus(new Decimal(updatedInvoice.paid)),
        );
        status = unPaid.equals(0)
          ? InvoiceStatus.PAID
          : InvoiceStatus.PART_PAID;
        await queryRunner.manager.save(Invoice, {
          id: updatedInvoice.id,
          paid: parseFloat(newPaid.toFixed(2)),
          unPaid: parseFloat(unPaid.toFixed(2)),
          status,
        });
        clonedInvoiceObj = {
          ...clonedInvoiceObj,
          paid: parseFloat(sumPaid.toFixed(2)),
          unPaid: parseFloat(unPaid.toFixed(2)),
          status,
        };
        // update status for order
        await queryRunner.manager.update(
          Order,
          { invoice: { id: updatedInvoice.id } },
          { status },
        );
      }

      // update status of order part paid
      if (updatedInvoice.childInvoices.length) {
        for (const childInvoice of updatedInvoice.childInvoices) {
          if (childInvoice.orders.length) {
            for (const order of childInvoice.orders) {
              await queryRunner.manager.update(
                Order,
                { id: order.id },
                { status },
              );
            }
          }
        }
      }

      // create credit history
      if (appointment?.customer?.id || updatedInvoice?.customer?.id) {
        let oldCredit = await queryRunner.manager.findOne(Credit, {
          where: {
            customer: {
              id: updatedInvoice?.customer?.id || updatedInvoice?.customer?.id,
            },
            creditSetting: { creditType: CreditType.OLD },
          },
        });

        let newCredit = await queryRunner.manager.findOne(Credit, {
          where: {
            customer: {
              id: appointment?.customer?.id || updatedInvoice?.customer?.id,
            },
            creditSetting: { creditType: CreditType.NEW },
          },
          relations: ['creditSetting'],
        });
        const creditTypeNeedCreate = await this.getCreditTypeInInvoice(
          updatedInvoice,
        );
        // create newCredit if not exists
        if (!newCredit && creditTypeNeedCreate.includes(CreditType.NEW)) {
          const creditSetting = await queryRunner.manager.findOne(
            CreditSetting,
            {
              where: {
                creditType: CreditType.NEW,
              },
            },
          );
          newCredit = await queryRunner.manager.getRepository(Credit).save({
            id: uuidv4(),
            customer: {
              id: appointment?.customer?.id || updatedInvoice?.customer?.id,
            },
            creditSetting,
            total: 0,
            creditBalance: 0,
            issueDate: new Date(),
            expiryDate: moment().toDate(),
            branch: dto?.branch?.id ?? appointment?.branch?.id ?? null,
          });
        }

        // create oldCredit if not exists
        if (!oldCredit && creditTypeNeedCreate.includes(CreditType.OLD)) {
          const creditSetting = await queryRunner.manager.findOne(
            CreditSetting,
            {
              where: {
                creditType: CreditType.OLD,
              },
            },
          );
          oldCredit = await queryRunner.manager.getRepository(Credit).save({
            id: uuidv4(),
            customer: {
              id: appointment?.customer?.id || updatedInvoice?.customer?.id,
            },
            creditSetting,
            total: 0,
            creditBalance: 0,
            issueDate: new Date(),
            expiryDate: moment().toDate(),
            branch: dto?.branch?.id ?? appointment?.branch?.id ?? null,
          });
        }

        // case buy membership
        if (updatedInvoice?.orders?.length > 0) {
          const countMembership = {
            [CreditType.NEW]: {
              total: 0,
              opening: 0,
              closing: 0,
            },
            [CreditType.OLD]: {
              total: 0,
              opening: 0,
              closing: 0,
            },
          };
          let openingBalanceNew = 0;
          let closingBalanceNew = 0;
          let openingBalanceOld = 0;
          let closingBalanceOld = 0;
          if (newCredit && creditTypeNeedCreate.includes(CreditType.NEW)) {
            openingBalanceNew = newCredit?.creditBalance;
          }
          if (oldCredit && creditTypeNeedCreate.includes(CreditType.OLD)) {
            openingBalanceOld = oldCredit?.creditBalance;
          }

          const newCreditHistoryInput = [];
          let expiryDateNewCredit = new Date();
          const oldCreditHistoryInput = [];
          let expiryDateOldCredit = new Date();

          for (const order of updatedInvoice.orders) {
            for (const item of order.items) {
              if (
                item.product?.type.toLowerCase() === ProductType.MEMBERSHIP &&
                total.equals(sumPaid)
              ) {
                // case credits
                if (
                  item.product.creditType === CreditType.NEW &&
                  !item.product.isPassport
                ) {
                  countMembership[CreditType.NEW].total +=
                    item?.product?.credit * (item?.quantity || 1) || 0;

                  if (item?.quantity > 0) {
                    for (let i = 0; i < item?.quantity; i++) {
                      const expiryDate = countExpiryDate(
                        new Date(),
                        item.product.periodUnit,
                        item.product.period,
                      );
                      expiryDateNewCredit = expiryDate;

                      closingBalanceNew =
                        openingBalanceNew + item.product.credit;

                      // Calculate the closing balance for this iteration
                      const clonedItem = { ...item };
                      clonedItem.quantity = 1;
                      const newClonedInvoiceObj = {
                        ...clonedInvoiceObj,
                        orders: [],
                      };
                      newClonedInvoiceObj.orders.push(clonedItem);

                      newCreditHistoryInput.push({
                        detail: newClonedInvoiceObj,
                        credit: { id: newCredit?.id },
                        paid: item.product.credit,
                        usable: item.product.credit,
                        isMembershipPkg: true,
                        opening: openingBalanceNew,
                        closing: closingBalanceNew,
                        expiryDate,
                        product: {
                          id: item.product.id,
                        },
                        invoice: {
                          id: invoice.id,
                        },
                        creditBefore: newCredit.total,
                      });
                      // Update opening balance for the next iteration
                      openingBalanceNew = closingBalanceNew;
                    }
                  }
                }
                // case old credit
                if (
                  item.product.creditType === CreditType.OLD &&
                  !item.product.isPassport
                ) {
                  countMembership[CreditType.OLD].total +=
                    item?.product?.credit * (item?.quantity || 1) || 0;

                  if (item?.quantity > 0) {
                    for (let i = 0; i < item?.quantity; i++) {
                      const expiryDate = countExpiryDate(
                        new Date(),
                        item.product.periodUnit,
                        item.product.period,
                      );
                      expiryDateOldCredit = expiryDate;

                      closingBalanceOld =
                        openingBalanceOld + item.product.credit;

                      // Calculate the closing balance for this iteration
                      const clonedItem = { ...item };
                      clonedItem.quantity = 1;
                      clonedInvoiceObj.orders = [];
                      clonedInvoiceObj.orders.push(clonedItem);

                      oldCreditHistoryInput.push({
                        detail: clonedInvoiceObj,
                        credit: { id: oldCredit?.id },
                        paid: item.product.credit,
                        usable: item.product.credit,
                        isMembershipPkg: true,
                        opening: openingBalanceOld,
                        closing: closingBalanceOld,
                        expiryDate,
                        product: {
                          id: item.product.id,
                        },
                        creditBefore: oldCredit.total,
                      });
                      // Update opening balance for the next iteration
                      openingBalanceOld = closingBalanceOld;
                    }
                  }
                }
              }
            }
          }

          if (countMembership[CreditType.NEW].total > 0 && newCredit) {
            await queryRunner.manager.getRepository(Credit).update(
              { id: newCredit.id },
              {
                total:
                  (newCredit?.total || 0) +
                  countMembership[CreditType.NEW].total,
                creditBalance:
                  (newCredit?.creditBalance || 0) +
                  countMembership[CreditType.NEW].total,
                expiryDate: expiryDateNewCredit,
                status: CreditStatus.VALID,
              },
            );

            if (oldCredit) {
              await queryRunner.manager.getRepository(Credit).update(
                { id: oldCredit.id },
                {
                  expiryDate: expiryDateNewCredit,
                },
              );
            }

            await queryRunner.manager
              .getRepository(CreditHistory)
              .insert(newCreditHistoryInput);
          }

          // case update for old credit
          if (countMembership[CreditType.OLD].total > 0 && oldCredit) {
            await queryRunner.manager.getRepository(Credit).update(
              { id: oldCredit.id },
              {
                total:
                  (oldCredit?.total || 0) +
                  countMembership[CreditType.OLD].total,
                creditBalance:
                  (oldCredit?.creditBalance || 0) +
                  countMembership[CreditType.OLD].total,
                expiryDate: expiryDateOldCredit,
                status: CreditStatus.VALID,
              },
            );

            if (newCredit) {
              await queryRunner.manager.getRepository(Credit).update(
                { id: newCredit.id },
                {
                  expiryDate: expiryDateOldCredit,
                },
              );
            }

            await queryRunner.manager
              .getRepository(CreditHistory)
              .insert(oldCreditHistoryInput);
          }
        }

        // case buy passport
        let expiredDatePassport = appointment.customer.expiryDate
          ? new Date(appointment.customer.expiryDate)
          : new Date();
        let updateExpireDate = false;
        let passportUsageLimit = 0;
        let passportHistory = {};
        for (const order of updatedInvoice?.orders) {
          for (const item of order?.items) {
            for (let i = 1; i <= item.quantity; i++) {
              if (item?.product?.isPassport) {
                expiredDatePassport = countExpiryDate(
                  expiredDatePassport,
                  item.product.periodUnit,
                  item.product.period,
                );
                updateExpireDate = true;
                passportUsageLimit += item.product.quantity;
                passportHistory = {
                  detail: {},
                  paid: item.product.price,
                  expiryDate: expiredDatePassport,
                  product: {
                    id: item.product.id,
                  },
                  usable: item.product.quantity,
                  invoice: {
                    id: updatedInvoice.id,
                  },
                  customer: {
                    id: appointment.customer.id,
                  },
                  isPassportHistory: true,
                };
              }
            }
          }
        }
        // update for customer expireDate
        if (updateExpireDate) {
          await queryRunner.manager.update(
            Customer,
            {
              id: appointment.customer.id,
            },
            {
              expiryDate: expiredDatePassport,
              passportUsageLimit,
            },
          );
          await queryRunner.manager.insert(CreditHistory, passportHistory);
        }

        // case payment method is credit
        if (updatedInvoice?.invoicePayments?.length > 0) {
          for (const payment of updatedInvoice.invoicePayments) {
            if (
              payment?.paymentMethod?.code.toLowerCase() === CreditType.OLD &&
              oldCredit
            ) {
              await queryRunner.manager.getRepository(Credit).update(
                { id: oldCredit.id }, // Criteria
                {
                  creditBalance:
                    oldCredit.creditBalance - payment.paid > 0
                      ? +(oldCredit.creditBalance - payment.paid).toFixed(2)
                      : 0,
                } as Partial<Credit>, // PartialEntity
              );

              const membershipUsable = await this.processMembershipUsable(
                queryRunner,
                oldCredit,
                payment.paid,
                invoice?.id,
              );
              await queryRunner.manager.getRepository(CreditHistory).insert({
                detail: updatedInvoice,
                credit: { id: oldCredit?.id },
                paid: payment.paid,
                opening: oldCredit?.creditBalance,
                closing: oldCredit?.creditBalance - payment.paid,
                membership: {
                  id: membershipUsable ? membershipUsable.id : undefined,
                },
                invoice: {
                  id: invoice?.id,
                },
              });
            }

            if (
              payment?.paymentMethod?.code.toLowerCase() === CreditType.NEW &&
              newCredit
            ) {
              await queryRunner.manager.getRepository(Credit).update(
                { id: newCredit.id }, // Criteria
                {
                  creditBalance:
                    newCredit.creditBalance - payment.paid > 0
                      ? +(newCredit.creditBalance - payment.paid).toFixed(2)
                      : 0,
                } as Partial<Credit>, // PartialEntity
              );

              const membershipUsable = await this.processMembershipUsable(
                queryRunner,
                newCredit,
                payment.paid,
                invoice?.id,
              );
              await queryRunner.manager.getRepository(CreditHistory).insert({
                detail: updatedInvoice,
                credit: { id: newCredit?.id },
                paid: payment.paid,
                opening: newCredit?.creditBalance,
                closing: newCredit?.creditBalance - payment.paid,
                membership: {
                  id: membershipUsable ? membershipUsable.id : undefined,
                },
                invoice: {
                  id: invoice?.id,
                },
              });
            }
          }
        }
      }

      // create commission logs by product
      if (updatedInvoice?.orders.length > 0 && status === InvoiceStatus.PAID) {
        updatedInvoice.orders.forEach(async (order) => {
          order.items.forEach(async (item) => {
            if (item.product?.type.toLowerCase() === ProductType.SERVICE) {
              if (item.employees.length > 0) {
                item.employees.forEach(async (emp) => {
                  await queryRunner.manager
                    .getRepository(CommissionLogs)
                    .insert({
                      quantity: item?.quantity,
                      price: item?.product?.price,
                      type: item?.product?.type,
                      employee: { id: emp?.id },
                      product: { id: item?.product?.id },
                      invoice: { id: updatedInvoice.id },
                    });
                });
              }
            } else if (
              item.product?.type.toLowerCase() === ProductType.MEMBERSHIP ||
              item.product?.type.toLowerCase() === ProductType.PRODUCT
            ) {
              await queryRunner.manager.getRepository(CommissionLogs).insert({
                quantity: item?.quantity,
                price: item?.product?.price,
                type: item?.product?.type,
                user: { id: updatedInvoice?.referral?.id },
                product: { id: item?.product?.id },
                invoice: { id: updatedInvoice.id },
              });
            }
          });
        });

        // case payment Part paid for services
        if (updatedInvoice?.childInvoices?.length > 0) {
          for (const childInvoice of updatedInvoice.childInvoices) {
            if (childInvoice?.status === InvoiceStatus.PART_PAID) {
              if (childInvoice?.orders?.length > 0) {
                childInvoice.orders.forEach(async (order) => {
                  order.items.forEach(async (item) => {
                    if (
                      item.product.type.toLowerCase() === ProductType.SERVICE
                    ) {
                      if (item.employees.length > 0) {
                        item.employees.forEach(async (emp) => {
                          await queryRunner.manager
                            .getRepository(CommissionLogs)
                            .insert({
                              quantity: item?.quantity,
                              price: item?.product?.price,
                              type: item?.product?.type,
                              employee: { id: emp?.id },
                              product: { id: item?.product?.id },
                              invoice: { id: childInvoice.id },
                            });
                        });
                      }
                    } else if (
                      item.product.type.toLowerCase() ===
                        ProductType.MEMBERSHIP ||
                      item.product.type.toLowerCase() === ProductType.PRODUCT
                    ) {
                      await queryRunner.manager
                        .getRepository(CommissionLogs)
                        .insert({
                          quantity: item?.quantity,
                          price: item?.product?.price,
                          type: item?.product?.type,
                          user: { id: childInvoice?.referral?.id },
                          product: { id: item?.product?.id },
                          invoice: { id: childInvoice.id },
                        });
                    }
                  });
                });
              }
            }
          }
        }
      }

      //send email coupon
      const couponCode = {};
      const couponUpdates = [];
      const issueCouponUpdates = new Map();

      // Collect all coupon items that need processing
      for (const order of updatedInvoice?.orders) {
        for (const orderItem of order.items) {
          if (
            orderItem.product?.type == ProductType.COUPON &&
            !orderItem.couponCode
          ) {
            if (!appointment.customer.email) {
              throw new BadRequestException(
                `Need update email for customer: ${appointment.customer.firstName} ${appointment.customer.lastName}`,
              );
            }

            // Get all active issue coupons in one query
            const issueCoupons = await queryRunner.manager.find(IssueCoupon, {
              where: {
                coupon: {
                  id: orderItem.product.id,
                  category: {
                    id: orderItem.product.category.id,
                  },
                },
                status: { name: RecordStatus.ACTIVE },
              },
              order: { issueDate: 'ASC' },
              relations: ['status', 'coupon', 'coupon.category'],
            });
            const totalRemaining = issueCoupons.reduce(
              (sum, issueCoupon) => sum + (issueCoupon.remain || 0),
              0,
            );
            if (totalRemaining < orderItem.quantity) {
              throw new BadRequestException(
                `Not enough available coupons. Requested: ${orderItem.quantity}, Available: ${totalRemaining}`,
              );
            }
            let lengthOfPaymentCoupon = orderItem.quantity;

            // Initialize product in couponCode map
            if (!couponCode[orderItem.product?.id]) {
              couponCode[orderItem.product?.id] = {
                name: orderItem.product?.name,
                periodUnit: orderItem.product?.periodUnit,
                period: orderItem.product?.period,
                codes: [],
              };
            }

            // Process each issue coupon
            for (const itemIssueCoupon of issueCoupons) {
              if (itemIssueCoupon.remain <= 0) continue;

              const currentBatchQuantity = Math.min(
                lengthOfPaymentCoupon,
                itemIssueCoupon.remain,
              );

              // Get coupon items in bulk
              const couponItems = await queryRunner.manager.find(CouponItem, {
                where: {
                  issueCoupon: { id: itemIssueCoupon.id },
                  email: IsNull(),
                  isUsed: false,
                },
                relations: ['issueCoupon'],
                order: { code: 'ASC' },
                take: currentBatchQuantity,
              });

              if (
                couponItems.length === 0 &&
                itemIssueCoupon.remain === currentBatchQuantity
              ) {
                const inactiveStatus = await queryRunner.manager.findOne(
                  Setting,
                  {
                    where: { name: 'Inactive', type: 'status' },
                  },
                );

                if (inactiveStatus) {
                  issueCouponUpdates.set(itemIssueCoupon.id, {
                    status: inactiveStatus,
                    remain: 0,
                  });
                }
              }

              // Prepare updates for issue coupon
              const shouldSetInactive =
                (couponItems.length === 0 &&
                  itemIssueCoupon.remain === currentBatchQuantity) ||
                itemIssueCoupon.remain === currentBatchQuantity;
              if (shouldSetInactive) {
                const inactiveStatus = await queryRunner.manager.findOne(
                  Setting,
                  {
                    where: { name: 'INACTIVE', type: 'STATUS' },
                  },
                );
                if (inactiveStatus) {
                  issueCouponUpdates.set(itemIssueCoupon.id, {
                    status: inactiveStatus,
                    remain: 0,
                  });
                }
              } else {
                issueCouponUpdates.set(itemIssueCoupon.id, {
                  used: itemIssueCoupon.used + currentBatchQuantity,
                  remain: itemIssueCoupon.remain - currentBatchQuantity,
                });
              }

              // Prepare coupon item updates
              for (const item of couponItems) {
                couponCode[orderItem.product?.id].codes.push(item.code);
                const purchaseDate = new Date();
                const expiryDate = countExpiryDate(
                  purchaseDate,
                  orderItem.product?.periodUnit,
                  orderItem.product?.period,
                );

                couponUpdates.push({
                  id: item.id,
                  startDate: purchaseDate,
                  expiryDate: expiryDate,
                  email: appointment.customer.email,
                });
              }

              lengthOfPaymentCoupon -= currentBatchQuantity;
              if (lengthOfPaymentCoupon <= 0) break;
            }

            if (lengthOfPaymentCoupon > 0) {
              throw new BadRequestException(
                `Not enough available coupons. Still need ${lengthOfPaymentCoupon} more coupons.`,
              );
            }
          }
        }
      }

      // Bulk update issue coupons
      for (const [id, updates] of issueCouponUpdates) {
        await queryRunner.manager.update(IssueCoupon, id, updates);
      }

      // Bulk update coupon items
      if (couponUpdates.length > 0) {
        await Promise.all(
          couponUpdates.map((update) =>
            queryRunner.manager.update(CouponItem, update.id, {
              startDate: update.startDate,
              expiryDate: update.expiryDate,
              email: update.email,
            }),
          ),
        );
      }

      // Send email for coupon codes
      if (Object.keys(couponCode).length > 0) {
        const templateHTML = await renderMJML(SEND_COUPON_CODE);
        const contentTemplate = handlebars.compile(templateHTML.html);
        const contentHTML = [];

        for (const item of Object.values(couponCode)) {
          const today = new Date();
          const expiredDate = countExpiryDate(
            today,
            item?.['periodUnit'],
            item?.['period'],
          );
          expiredDate.setHours(23);
          expiredDate.setMinutes(59);
          expiredDate.setSeconds(59);
          expiredDate.setMilliseconds(999);

          const dayFormat = String(expiredDate.getDate()).padStart(2, '0');
          const monthFormat = String(expiredDate.getMonth() + 1).padStart(
            2,
            '0',
          );
          const yearFormat = expiredDate.getFullYear();

          for (const [index, code] of item['codes'].entries()) {
            contentHTML.push({
              name: item['name'],
              code: code,
              expiredDate: `${dayFormat}/${monthFormat}/${yearFormat}`,
              isFirst: index === 0,
              isRectangle: index === item['codes'].length - 1,
              length: item['codes'].length,
            });
          }
        }

        const html = contentTemplate({ coupons: contentHTML });
        this.mailerService.sendMail({
          to: appointment.customer.email,
          html,
          subject: 'Coupon Code',
          text: 'Coupon Code',
        });
      }

      // update isUsed for discount by code
      if (dto.discounts) {
        const discountCodes = [];
        for (const i of dto.discounts) {
          if (i.couponCode) {
            discountCodes.push(i.couponCode);
          }
        }

        // Mark coupon items as used and update IssueCoupon remain counts
        const couponItems = await queryRunner.manager.find(CouponItem, {
          where: { code: In(discountCodes) },
          relations: ['issueCoupon'],
        });

        await queryRunner.manager.getRepository(CouponItem).update(
          {
            code: In(discountCodes),
          },
          {
            isUsed: true,
          },
        );

        // Update IssueCoupon remain and used counts
        for (const couponItem of couponItems) {
          if (couponItem.issueCoupon) {
            const allCouponItems = await queryRunner.manager.find(CouponItem, {
              where: { issueCoupon: { id: couponItem.issueCoupon.id } },
            });

            const usedCount = allCouponItems.filter(
              (item) => item.isUsed || discountCodes.includes(item.code),
            ).length;
            const totalCount = allCouponItems.length;
            const remainCount = totalCount - usedCount;

            await queryRunner.manager.update(
              IssueCoupon,
              couponItem.issueCoupon.id,
              {
                used: usedCount,
                remain: remainCount,
              },
            );

            // If all coupons are used, mark as inactive
            if (remainCount === 0) {
              const inactiveStatus = await queryRunner.manager.findOne(
                Setting,
                {
                  where: { name: 'INACTIVE', type: 'STATUS' },
                },
              );
              if (inactiveStatus) {
                await queryRunner.manager.update(
                  IssueCoupon,
                  couponItem.issueCoupon.id,
                  {
                    status: inactiveStatus,
                  },
                );
              }
            }
          }
        }
      }

      await this.updatePayload(queryRunner, updatedInvoice);
      // commit transaction now:
      await queryRunner.commitTransaction();

      await this.repo.manager
        .getRepository(CreditHistory)
        .createQueryBuilder()
        .update()
        .set({
          detail: () =>
            `jsonb_set(detail, '{date}', '"${updatedInvoice.date.toISOString()}"', false)`,
        })
        .where("detail->>'id' = :invoiceId", { invoiceId: updatedInvoice.id })
        .execute();

      const orders = await this.repo.manager.find(Order, {
        where: { invoice: { id: updatedInvoice.id } },
        relations: ['appointment'],
      });
      for (const order of orders) {
        if (order.appointment && order.appointment.startTime) {
          const oldStartTime = moment(order.appointment.startTime);
          const newDateMoment = moment(updatedInvoice.date);
          const newStartTime = oldStartTime
            .set({
              year: newDateMoment.year(),
              month: newDateMoment.month(),
              date: newDateMoment.date(),
            })
            .toDate();
          await this.repo.manager.update(
            'Appointment',
            { id: order.appointment.id },
            { startTime: newStartTime },
          );
        }
      }
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(error);
    } finally {
      // you need to release query runner which is manually created:
      await queryRunner.release();
    }
    return await this.repo.findOne({
      where: {
        id: invoice.id,
      },
      relations: [
        'branch',
        'referral',
        'appointment',
        'parentInvoice',
        'childInvoices',
        'customer',
        'customer.credits',
        'customer.credits.creditSetting',
        'invoicePayments',
        'invoicePayments.paymentMethod',
        'orders',
        'orders.items',
        'orders.items.employees',
        'orders.items.product',
        'orders.items.product.category',
      ],
    });
  }

  async updateOneInvoice(dto: any, id: string) {
    const findInvoice = await this.repo.findOne({
      where: { id },
      relations: [
        'branch',
        'referral',
        'appointment',
        'invoiceCoupon',
        'parentInvoice',
        'childInvoices',
        'childInvoices.orders',
        'childInvoices.orders.items',
        'childInvoices.orders.items.employees',
        'childInvoices.orders.items.product',
        'customer',
        'customer.credits',
        'customer.credits.creditSetting',
        'invoicePayments',
        'invoicePayments.paymentMethod',
        'orders',
        'orders.items',
        'orders.items.employees',
        'orders.items.product',
        'orders.items.product.category',
      ],
    });
    if (!findInvoice) {
      throw new BadRequestException('Invoice not found');
    }
    let sumPaid = new Decimal(0);
    if (dto?.invoicePayments?.length > 0) {
      for (const payment of dto.invoicePayments) {
        if (payment.paymentMethod?.code.toLowerCase() === PaymentMethod.CASH) {
          payment.paid = new Decimal(payment.paid).plus(
            new Decimal(payment?.roundNumber ?? 0),
          );
        }
        sumPaid = sumPaid.plus(new Decimal(payment.paid));
      }
    }
    let total = new Decimal(0);
    let totalBeforeTax = new Decimal(0);
    let subTotal = new Decimal(0);
    let sumDiscount = new Decimal(0);
    // sum discount
    if (dto.discounts) {
      for (const i of dto.discounts) {
        if (i.discountMoney) {
          sumDiscount = sumDiscount.plus(new Decimal(i.discountMoney));
        }
      }
    }
    const calTax = await this.processCalculateTax(dto, findInvoice.branch, id);
    if (calTax.orderItems && calTax.orderItems.length) {
      total = new Decimal(calTax ? calTax.totalPaid : 0);
      totalBeforeTax = new Decimal(calTax ? calTax.totalBeforeTax : 0);
      subTotal = new Decimal(calTax ? calTax.subTotal : 0);
      // sumDiscount = new Decimal(calTax ? calTax.sumDiscount : 0);
      if (total.lessThan(0)) {
        throw new BadRequestException(
          `The amount on the invoice is invalid. The amount should be ${total}`,
        );
      }
      if (sumPaid.greaterThan(total)) {
        throw new BadRequestException(
          `The amount paid is invalid. The amount should be ${total}`,
        );
      }
    }
    if (!sumPaid.equals(total) && dto.status === InvoiceStatus.PAID) {
      throw new BadRequestException(
        `The amount paid is invalid. The amount should be ${total}`,
      );
    }
    if (sumPaid.greaterThan(total) && dto.status === InvoiceStatus.PART_PAID) {
      throw new BadRequestException(
        `The amount paid is invalid. The amount should be ${total}`,
      );
    }
    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      if (calTax.orderItems && calTax.orderItems.length) {
        const unPaid = new Decimal(total.minus(sumPaid));
        const status = unPaid.equals(0)
          ? InvoiceStatus.PAID
          : InvoiceStatus.PART_PAID;
        await queryRunner.manager.save(Invoice, {
          id: findInvoice.id,
          total: parseFloat(total.toFixed(2)),
          paid: parseFloat(sumPaid.toFixed(2)),
          unPaid: parseFloat(unPaid.toFixed(2)),
          totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
          subTotal: parseFloat(subTotal.toFixed(2)),
          discount: parseFloat(sumDiscount.toFixed(2)),
          status,
          invoiceCoupon: calTax.invoiceDiscountIds,
          note: dto?.note,
        });
        await queryRunner.manager.update(
          Order,
          { invoice: { id: findInvoice.id } },
          { status },
        );

        await this.processUpdateInvoicePayments(
          queryRunner,
          findInvoice,
          findInvoice.customer,
          findInvoice.invoicePayments,
          dto?.invoicePayments,
          status,
        );

        await this.updatePayload(queryRunner, findInvoice);
      } else {
        // When voiding invoice, reset financial fields to 0 and update status
        await queryRunner.manager.save(Invoice, {
          id: findInvoice.id,
          status: InvoiceStatus.VOID,
          voidDate: new Date(),
          note: dto?.note,
          total: 0,
          paid: 0,
          unPaid: 0,
          totalBeforeTax: 0,
          subTotal: 0,
          discount: 0,
        });
        await queryRunner.manager.update(
          Order,
          { invoice: { id: findInvoice.id } },
          { status: InvoiceStatus.VOID },
        );

        await this.processUpdateInvoicePayments(
          queryRunner,
          findInvoice,
          findInvoice.customer,
          findInvoice.invoicePayments,
          dto?.invoicePayments,
          InvoiceStatus.VOID,
        );
      }
      await queryRunner.commitTransaction();

      // Update detail.date in CreditHistory for this invoice
      await this.repo.manager
        .getRepository(CreditHistory)
        .createQueryBuilder()
        .update()
        .set({
          detail: () =>
            `jsonb_set(detail, '{date}', '"${findInvoice.date.toISOString()}"', false)`,
        })
        .where("detail->>'id' = :invoiceId", { invoiceId: findInvoice.id })
        .execute();
    } catch (error) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }

    const invoiceDetail = await this.repo.findOne({
      where: { id },
      relations: [
        'branch',
        'referral',
        'appointment',
        'invoiceCoupon',
        'parentInvoice',
        'childInvoices',
        'childInvoices.orders',
        'childInvoices.orders.items',
        'childInvoices.orders.items.employees',
        'childInvoices.orders.items.product',
        'customer',
        'customer.credits',
        'customer.credits.creditSetting',
        'invoicePayments',
        'invoicePayments.paymentMethod',
        'orders',
        'orders.items',
        'orders.items.employees',
        'orders.items.product',
        'orders.items.product.category',
      ],
    });
    await this.processInvoiceNewPaid(
      invoiceDetail,
      null,
      invoiceDetail.childInvoices,
    );
    return invoiceDetail;
  }

  async backDate(id: string, body: BackDateDto) {
    const invoice = await this.repo.manager.findOne(Invoice, {
      where: { id },
    });

    if (!invoice) {
      throw new BadRequestException('Invoice not found');
    }

    if (!moment(body.date).isValid()) {
      throw new BadRequestException('Date is valid');
    }

    // Extract timezone offset from the date string or use system timezone
    const clientZone =
      body.date.match(/([+-]\d{2}:\d{2}|[+-]\d{4})$/)?.[0] ||
      moment().format('Z');

    // Parse date without timezone, then apply timezone
    const dateWithoutTZ = body.date
      .replace(/([+-]\d{2}:\d{2}|[+-]\d{4})$/, '')
      .trim();
    const localDate = moment.parseZone(dateWithoutTZ + clientZone);

    // Convert to UTC for storage
    const newDate = localDate.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');

    // Save old date for recalculation
    const oldDate = invoice.date;
    const updatedInvoice = await this.repo.manager.save(Invoice, {
      ...invoice,
      date: newDate,
    });

    // Recalculate daily sales for old and new date
    if (this.salesService && oldDate) {
      let oldDateStr: string;
      if (oldDate instanceof Date) {
        oldDateStr = oldDate.toISOString();
      } else {
        oldDateStr = String(oldDate);
      }
    }

    await this.repo.manager
      .getRepository(CreditHistory)
      .createQueryBuilder()
      .update()
      .set({
        detail: () => `jsonb_set(detail, '{date}', '"${newDate}"', false)`,
      })
      .where("detail->>'id' = :invoiceId", { invoiceId: updatedInvoice.id })
      .execute();

    const orders = await this.repo.manager.find(Order, {
      where: { invoice: { id: updatedInvoice.id } },
      relations: ['appointment'],
    });
    for (const order of orders) {
      if (order.appointment && order.appointment.startTime) {
        const oldStartTime = moment(order.appointment.startTime);
        const newDateMoment = moment(newDate);
        const newStartTime = oldStartTime
          .set({
            year: newDateMoment.year(),
            month: newDateMoment.month(),
            date: newDateMoment.date(),
          })
          .toDate();
        await this.repo.manager.update(
          'Appointment',
          { id: order.appointment.id },
          { startTime: newStartTime },
        );
      }
    }

    return updatedInvoice;
  }

  // async convertToDays(periodUnit, period) {
  //   const daysInMonth = 30;
  //   switch (periodUnit) {
  //     case PeriodUnit.YEAR:
  //       return period * 365;
  //     case PeriodUnit.MONTH:
  //       return period * daysInMonth;
  //     case PeriodUnit.DAY:
  //       return period;
  //     default:
  //       throw new Error('Invalid period unit');
  //   }
  // }

  async getList(
    crudReq: CrudRequest,
    { limit, keySearch, page }: InvoiceQueryDto,
  ) {
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;

    // Default to current day in UTC
    const data: any = {
      startTime: moment
        .utc()
        .startOf('day')
        .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
      endTime: moment.utc().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
    };

    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'date' && f.operator === '$between') {
          // Parse dates in UTC, preserving the timezone information
          data.startTime = moment
            .parseZone(f.value[0])
            .utc()
            .startOf('day')
            .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
          data.endTime = moment
            .parseZone(f.value[1])
            .utc()
            .endOf('day')
            .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        }
      }
    }

    const builder = await super.getManyBuilder(crudReq);
    builder
      .andWhere('"Invoice"."date" >= :startDate', {
        startDate: data.startTime,
      })
      .andWhere('"Invoice"."date" <= :endDate', {
        endDate: data.endTime,
      });
    // if (keySearch) {
    //   builder.andWhere(
    //     new Brackets((qb) => {
    //       qb.where('"Invoice"."code" ILike :keySearch', {
    //         keySearch: `%${keySearch}%`,
    //       });
    //     }),
    //   );
    //   // builder.andWhere('"Invoice"."code" ILike :keySearch', {
    //   //   keySearch: `%${keySearch}%`,
    //   // });
    // }
    builder.take(qLimit);
    builder.skip(offset);
    const [invoiceList, total] = await builder.getManyAndCount();
    for (const invoice of invoiceList) {
      await this.processInvoiceNewPaid(invoice, invoice.parentInvoice, null);
    }

    return this.createPageInfo<any>(
      invoiceList,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  async getOneInvoice(crudRequest: CrudRequest): Promise<any> {
    const invoiceDetail = await super.getOne(crudRequest);
    // invoiceDetail.invoiceCoupon = (invoiceDetail.payload as any)?.invoiceCoupon;
    await this.processInvoiceNewPaidForDetail(
      invoiceDetail,
      null,
      invoiceDetail.childInvoices,
    );
    return invoiceDetail;
  }

  async getCreditTypeInInvoice(invoice: Invoice) {
    const creditType = [];
    for (const order of invoice?.orders) {
      for (const item of order?.items) {
        if (
          item?.product?.type?.toLowerCase() === ProductType.MEMBERSHIP &&
          item?.product?.creditType === CreditType.OLD &&
          !item?.product?.isPassport
        ) {
          creditType.push(item?.product?.creditType);
        }
        if (
          item?.product?.type?.toLowerCase() === ProductType.MEMBERSHIP &&
          item?.product?.creditType === CreditType.NEW &&
          !item?.product?.isPassport
        ) {
          creditType.push(item?.product?.creditType);
        }
      }
    }
    return creditType;
  }

  async processCalculateTax(dto: any, branch: any, invoiceId?: string) {
    if (dto?.orders && dto?.orders?.length) {
      let totalBeforeTax = new Decimal(0);
      const orderItems = [];
      for (const order of dto.orders) {
        const orderInfo = await this.repo.manager.findOne(Order, {
          where: { id: order?.id },
          relations: ['items', 'items.product'],
        });
        if (orderInfo) {
          totalBeforeTax = totalBeforeTax.plus(
            new Decimal(orderInfo?.subTotal ?? 0),
          );
          (orderInfo.items || []).forEach((item) => {
            orderItems.push(item);
          });
        }
      }
      const subTotal = new Decimal(totalBeforeTax);
      let sumDiscount = new Decimal(0);
      let invoiceDiscountIds = [];
      const oldInvoiceDiscountIds = [];
      const oldInvoiceDiscounts = [];
      if (invoiceId) {
        // Case: updating the invoice when adding a discount get data the previous discount intact
        const dtInvoice = await this.repo.manager.findOne(Invoice, {
          where: {
            id: invoiceId,
          },
          relations: ['invoiceCoupon', 'parentInvoice', 'childInvoices'],
        });
        const invoiceCoupons = dtInvoice.invoiceCoupon
          ? dtInvoice.invoiceCoupon
          : [];
        if (invoiceCoupons.length) {
          for (const invoiceCoupon of invoiceCoupons) {
            const objInvoiceCoupon = _.find(dto?.discounts, {
              id: invoiceCoupon.id,
            });
            if (objInvoiceCoupon) {
              oldInvoiceDiscountIds.push({ id: invoiceCoupon.id });
              oldInvoiceDiscounts.push({
                id: invoiceCoupon.id,
                discountMoney: objInvoiceCoupon?.discountMoney,
                order: invoiceCoupon.order,
              });
            }
          }
        }
      }
      // Handle price discount
      let flagDiscountZero = false;
      if (dto?.discounts && dto?.discounts.length) {
        let discountOrder = Number(oldInvoiceDiscounts.length + 1);
        for (const discount of dto.discounts) {
          if (!discount?.id) {
            let couponItem;
            if (discount.couponType === CouponType.CODE) {
              const conditions = {
                code: discount.couponCode,
                isUsed: Not(true),
                issueCoupon: {
                  status: {
                    name: RecordStatus.ACTIVE,
                  },
                },
              };
              couponItem = await this.repo.manager.findOne(CouponItem, {
                where: [
                  {
                    ...conditions,
                    expiryDate: MoreThanOrEqual(new Date()),
                  },
                  {
                    ...conditions,
                    expiryDate: IsNull(),
                  },
                ],
                relations: [
                  'issueCoupon',
                  'issueCoupon.coupon',
                  'issueCoupon.status',
                ],
              });
              if (!couponItem || !couponItem?.issueCoupon?.coupon) {
                throw new BadRequestException('Coupon code is invalid');
              }

              // Check if this is the last available coupon
              const issueCoupon = couponItem.issueCoupon;
              const couponItems = await this.repo.manager.find(CouponItem, {
                where: { issueCoupon: { id: issueCoupon.id } },
              });

              const usedCoupons = couponItems.filter(
                (item) => item.isUsed,
              ).length;
              const totalCoupons = couponItems.length;

              // If this is the last available coupon, update status to inactive
              if (usedCoupons === totalCoupons - 1) {
                // -1 because current coupon is not yet marked as used
                const inactiveStatus = await this.repo.manager.findOne(
                  Setting,
                  {
                    where: { name: 'Inactive', type: 'status' },
                  },
                );

                if (inactiveStatus) {
                  await this.repo.manager.update(IssueCoupon, issueCoupon.id, {
                    status: inactiveStatus,
                    remain: 0,
                    used: totalCoupons,
                  });
                }
              } else {
                // Update used and remain counts
                await this.repo.manager.update(IssueCoupon, issueCoupon.id, {
                  used: usedCoupons + 1,
                  remain: totalCoupons - (usedCoupons + 1),
                });
              }

              if (!couponItem.expiryDate) {
                const issueCoupon = couponItem.issueCoupon;
                // handle check expiry of coupon codes

                const expiryDate = countExpiryDate(
                  issueCoupon?.issueDate,
                  issueCoupon?.coupon?.periodUnit,
                  issueCoupon?.coupon?.period,
                );

                const expiredDateTime = moment(expiryDate);
                const currentDateTime = moment();
                if (expiredDateTime.isBefore(currentDateTime)) {
                  throw new BadRequestException('Coupon code is invalid');
                }
              }

              // Mark coupon as used
              await this.repo.manager.update(CouponItem, couponItem.id, {
                isUsed: true,
              });
            }
            const invoiceDiscount = await this.repo.manager.save(
              InvoiceCoupon,
              {
                couponItem: {
                  id: couponItem ? couponItem.id : null,
                },
                couponCode: discount?.couponCode,
                couponName: discount?.couponName,
                couponType: discount?.couponType,
                percent: discount?.percent ?? 0,
                discountValue: discount.discountMoney,
                discountProduct: {
                  id: discount?.discountProductId,
                },
                order: discountOrder,
                branch,
              },
            );
            discountOrder++;
            invoiceDiscountIds.push({ id: invoiceDiscount.id });
            const discountMoney = new Decimal(discount?.discountMoney ?? 0);
            sumDiscount = sumDiscount.plus(discountMoney);
            if (discountMoney.greaterThan(0)) {
              totalBeforeTax = totalBeforeTax.minus(discountMoney);
            }
            // Do NOT reset totalBeforeTax to 0 if discountMoney is 0
          }
        }
        // Case: updating the invoice when adding a discount keeps the previous discount intact
        if (oldInvoiceDiscounts.length) {
          for (const oldInvoiceDiscount of oldInvoiceDiscounts) {
            await this.repo.manager.save(InvoiceCoupon, {
              id: oldInvoiceDiscount.id,
              discountValue: oldInvoiceDiscount.discountMoney,
            });
            const oldDiscountMoney = new Decimal(
              oldInvoiceDiscount?.discountMoney ?? 0,
            );
            sumDiscount = sumDiscount.plus(oldDiscountMoney);
            if (oldDiscountMoney.greaterThan(0)) {
              totalBeforeTax = totalBeforeTax.minus(oldDiscountMoney);
            }
            // Do NOT reset totalBeforeTax to 0 if oldDiscountMoney is 0
          }
        }
        if (totalBeforeTax.lessThanOrEqualTo(0)) {
          flagDiscountZero = true;
        }
      }
      // End: Handle price discount
      // Handle tax of price has been reduced
      let totalCreditPaid = new Decimal(0);
      let flagCreditZero = false;
      if (dto?.invoicePayments?.length > 0 && totalBeforeTax.greaterThan(0)) {
        for (const payment of dto.invoicePayments) {
          let totalCashPaid = new Decimal(0);
          const paymentPaid = new Decimal(payment.paid);
          if (
            payment.paymentMethod?.code.toLowerCase() === CreditType.NEW ||
            payment.paymentMethod?.code.toLowerCase() === CreditType.OLD
          ) {
            totalBeforeTax = totalBeforeTax.minus(paymentPaid);
            totalCreditPaid = totalCreditPaid.plus(paymentPaid);
            if (totalBeforeTax.lessThanOrEqualTo(0)) {
              flagCreditZero = true;
            }
          }
          if (
            payment.paymentMethod?.code.toLowerCase() === PaymentMethod.CASH
          ) {
            totalCashPaid = totalCashPaid.plus(paymentPaid);
            totalCashPaid = await this.roundToNearestHalf(totalCashPaid);
            payment.paid = parseFloat(totalCashPaid.toFixed(2));
            payment.roundNumber = parseFloat(
              paymentPaid.minus(totalCashPaid).toFixed(2),
            );
          }
        }
      }
      let tax = new Decimal(0);
      if (totalBeforeTax.greaterThan(0)) {
        tax = totalBeforeTax.times(0.09);
      }
      // End: Handle tax of price has been reduced
      let totalPaid;
      if (flagDiscountZero && !flagCreditZero) {
        totalPaid = new Decimal(0);
      } else if (!flagDiscountZero && !flagCreditZero) {
        totalPaid = totalBeforeTax.plus(tax).plus(totalCreditPaid);
      } else {
        totalPaid = subTotal;
        if (sumDiscount.greaterThan(0)) {
          totalPaid = subTotal.minus(sumDiscount);
        }
      }
      invoiceDiscountIds = invoiceDiscountIds.concat(oldInvoiceDiscountIds);
      return {
        totalPaid: parseFloat(totalPaid.toFixed(2)),
        totalBeforeTax: totalBeforeTax.greaterThan(0)
          ? parseFloat(totalBeforeTax.toFixed(2))
          : 0,
        subTotal: parseFloat(subTotal.toFixed(2)),
        sumDiscount: parseFloat(sumDiscount.toFixed(2)),
        invoiceDiscountIds,
        orderItems,
      };
    }
    return null;
  }

  async processUpdateInvoicePayments(
    queryRunner,
    invoice: any,
    customer: any,
    oldInvoicePayments: any,
    newInvoicePayments: any,
    status?: any,
  ) {
    const newCredit = await queryRunner.manager.findOne(Credit, {
      where: {
        customer: {
          id: customer.id,
        },
        creditSetting: { creditType: CreditType.NEW },
      },
      relations: ['creditSetting'],
    });
    const oldCredit = await queryRunner.manager.findOne(Credit, {
      where: {
        customer: {
          id: customer.id,
        },
        creditSetting: { creditType: CreditType.OLD },
      },
      relations: ['creditSetting'],
    });
    let newCreditBalance = newCredit?.creditBalance ?? null;
    let oldCreditBalance = oldCredit?.creditBalance ?? null;
    // Handle data old of invoice payment
    if (oldInvoicePayments && oldInvoicePayments.length) {
      for (const oldInvoicePayment of oldInvoicePayments) {
        if (
          (newCreditBalance || newCreditBalance >= 0) &&
          oldInvoicePayment.paymentMethod &&
          oldInvoicePayment.paymentMethod.code === CreditType.NEW
        ) {
          const refundBalance = (
            newCreditBalance + oldInvoicePayment.paid
          ).toFixed(2);
          await queryRunner.manager
            .getRepository(Credit)
            .update({ id: newCredit.id }, {
              creditBalance: refundBalance,
            } as Partial<Credit>);
          await this.processRefundMembershipUsable(
            queryRunner,
            newCredit,
            oldInvoicePayment.paid,
            [],
          );
          await queryRunner.manager.getRepository(CreditHistory).delete({
            credit: { id: newCredit?.id },
            invoice: {
              id: invoice.id,
            },
          });
          newCreditBalance = refundBalance;
        }
        if (
          (oldCreditBalance || oldCreditBalance >= 0) &&
          oldInvoicePayment.paymentMethod &&
          oldInvoicePayment.paymentMethod.code === CreditType.OLD
        ) {
          const refundBalance = (
            oldCreditBalance + oldInvoicePayment.paid
          ).toFixed(2);
          await queryRunner.manager
            .getRepository(Credit)
            .update({ id: oldCredit.id }, {
              creditBalance: refundBalance,
            } as Partial<Credit>);
          await this.processRefundMembershipUsable(
            queryRunner,
            oldCredit,
            oldInvoicePayment.paid,
            [],
          );
          await queryRunner.manager.getRepository(CreditHistory).delete({
            credit: { id: oldCredit?.id },
            invoice: {
              id: invoice.id,
            },
          });
          oldCreditBalance = refundBalance;
        }

        if (status !== InvoiceStatus.VOID) {
          await queryRunner.manager.delete(InvoicePaymentMethod, {
            id: oldInvoicePayment.id,
          });
        }
        if (
          status === InvoiceStatus.VOID &&
          (oldInvoicePayment.paymentMethod.code === CreditType.OLD ||
            oldInvoicePayment.paymentMethod.code === CreditType.NEW)
        ) {
          await queryRunner.manager.delete(InvoicePaymentMethod, {
            id: oldInvoicePayment.id,
          });
        }
      }
    }
    // Handle data new of invoice payment
    if (
      newInvoicePayments &&
      newInvoicePayments.length &&
      status !== InvoiceStatus.VOID
    ) {
      const invoicePaymentIds = [];
      for (const newInvoicePayment of newInvoicePayments) {
        const invoicePayment = await queryRunner.manager.save(
          InvoicePaymentMethod,
          {
            paymentMethod: {
              id: newInvoicePayment.paymentMethod.id,
            },
            paid: parseFloat(newInvoicePayment.paid.toFixed(2)),
            branch: invoice?.branch?.id ?? null,
            billCode: newInvoicePayment?.billCode ?? null,
            roundNumber:
              parseFloat(newInvoicePayment?.roundNumber.toFixed(2)) ?? 0,
          },
        );
        invoicePaymentIds.push(invoicePayment.id);
        if (
          (oldCreditBalance || oldCreditBalance >= 0) &&
          newInvoicePayment.paymentMethod &&
          newInvoicePayment.paymentMethod.code === CreditType.OLD
        ) {
          await queryRunner.manager
            .getRepository(Credit)
            .update({ id: oldCredit.id }, {
              creditBalance:
                oldCreditBalance - newInvoicePayment.paid > 0
                  ? +(oldCreditBalance - newInvoicePayment.paid).toFixed(2)
                  : 0,
            } as Partial<Credit>);
          const membershipUsable = await this.processMembershipUsable(
            queryRunner,
            oldCredit,
            newInvoicePayment.paid,
            invoice?.id,
          );
          await queryRunner.manager.getRepository(CreditHistory).insert({
            detail: invoice,
            credit: { id: oldCredit?.id },
            paid: newInvoicePayment.paid,
            opening: oldCreditBalance,
            closing: oldCreditBalance - newInvoicePayment.paid,
            membership: {
              id: membershipUsable ? membershipUsable.id : undefined,
            },
            invoice: {
              id: invoice?.id,
            },
          });
        }
        if (
          (newCreditBalance || newCreditBalance >= 0) &&
          newInvoicePayment.paymentMethod &&
          newInvoicePayment.paymentMethod.code === CreditType.NEW
        ) {
          await queryRunner.manager
            .getRepository(Credit)
            .update({ id: newCredit.id }, {
              creditBalance:
                newCreditBalance - newInvoicePayment.paid > 0
                  ? +(newCreditBalance - newInvoicePayment.paid).toFixed(2)
                  : 0,
            } as Partial<Credit>);
          const membershipUsable = await this.processMembershipUsable(
            queryRunner,
            newCredit,
            newInvoicePayment.paid,
            invoice?.id,
          );
          await queryRunner.manager.getRepository(CreditHistory).insert({
            detail: invoice,
            credit: { id: newCredit?.id },
            paid: newInvoicePayment.paid,
            opening: newCreditBalance,
            closing: newCreditBalance - newInvoicePayment.paid,
            membership: {
              id: membershipUsable ? membershipUsable.id : undefined,
            },
            invoice: {
              id: invoice?.id,
            },
          });
        }
      }
      const invoicePaymentIdArr = invoicePaymentIds.map((id) => {
        return { id };
      });
      await queryRunner.manager.save(Invoice, {
        id: invoice.id,
        invoicePayments: invoicePaymentIdArr,
      });
    }
  }

  async processInvoiceNewPaid(
    invoice: any,
    parentInvoice: any,
    childInvoices: any,
  ) {
    // Handle voided invoices - reset newPaid to zero values
    if (invoice.status === InvoiceStatus.VOID) {
      invoice['newPaid'] = {
        totalPaid: 0,
        subTotal: 0,
        totalBeforeTax: 0,
        invoiceCoupon: [],
        isEdited: false,
      };
      return;
    }

    if (invoice.orders.length > 0) {
      invoice.orders.forEach((order) => {
        if (order.payload) {
          const orderPayload: any = order.payload;
          order.items = orderPayload.items;
        }
      });
      if (childInvoices) {
        const newChildInvoices = invoice.childInvoices;
        if (newChildInvoices.length) {
          for (let i = 0; i < newChildInvoices.length; i++) {
            const child = newChildInvoices[i];
            const childInvoiceDetail = await this.repo.findOne({
              where: { id: child.id },
              relations: ['orders', 'appointment'],
            });
            childInvoiceDetail.orders.forEach((order) => {
              if (order.payload) {
                const orderPayload: any = order.payload;
                order.items = orderPayload.items;
              }
            });
            newChildInvoices[i] = childInvoiceDetail;
          }
        }
      }
    }
    if (parentInvoice) {
      invoice.parentInvoice = await this.repo.findOne({
        where: { id: invoice.parentInvoice?.id },
      });
    }
    let sumDiscount = new Decimal(0);
    let sumPriceForDiscount = new Decimal(0);
    let sumSubTotal = new Decimal(0);
    let isEdited = false;
    for (const order of invoice.orders) {
      const orderPayload: any = order.payload;
      for (const productItem of orderPayload.items) {
        const priceItem = !productItem?.couponCode
          ? new Decimal(productItem.quantity).times(
              new Decimal(productItem?.product?.price),
            )
          : new Decimal(0);
        if (!productItem.product.isNotApplyDiscount) {
          sumPriceForDiscount = sumPriceForDiscount.plus(priceItem);
        }
      }
      sumSubTotal = sumSubTotal.plus(new Decimal(order.subTotal));
      if (order.isEdited) {
        isEdited = order.isEdited;
      }
    }
    if (invoice.invoiceCoupon) {
      const discountMoney = await this.processValueDiscount(
        invoice,
        sumPriceForDiscount,
      );
      sumDiscount = sumDiscount.plus(new Decimal(discountMoney));
    }
    let totalBeforeTax = sumSubTotal.minus(sumDiscount);
    let totalCreditPaid = new Decimal(0);
    for (const payment of invoice.invoicePayments) {
      if (
        payment.paymentMethod?.code.toLowerCase() === CreditType.NEW ||
        payment.paymentMethod?.code.toLowerCase() === CreditType.OLD
      ) {
        totalBeforeTax = totalBeforeTax.minus(new Decimal(payment.paid));
        totalCreditPaid = totalCreditPaid.plus(new Decimal(payment.paid));
      }
    }
    const newTotalPaid = totalBeforeTax.isPositive()
      ? totalBeforeTax.plus(totalBeforeTax.times(0.09)).plus(totalCreditPaid)
      : new Decimal(0);
    invoice['newPaid'] = {
      totalPaid: parseFloat(newTotalPaid.toFixed(2)),
      subTotal: parseFloat(sumSubTotal.toFixed(2)),
      totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
      invoiceCoupon: invoice.invoiceCoupon,
      isEdited,
    };
  }

  async processMembershipUsable(
    queryRunner: any,
    credit: any,
    paid: any,
    invoiceId: string,
  ) {
    const membershipUsable = await queryRunner.manager
      .getRepository(CreditHistory)
      .createQueryBuilder('creditHistory')
      .where('creditHistory.credit.id = :creditId', { creditId: credit.id })
      .andWhere('creditHistory.usable > 0')
      .andWhere('creditHistory.expiryDate IS NOT NULL')
      .andWhere('creditHistory.purged IS NULL')
      .andWhere('creditHistory.isMembershipPkg = :isMembershipPkg', {
        isMembershipPkg: true,
      })
      .orderBy('creditHistory.expiryDate', 'ASC')
      .getOne();
    if (membershipUsable) {
      const usable = new Decimal(membershipUsable.usable);
      const payment = new Decimal(paid);
      const remain = usable.minus(payment);
      if (remain.lessThan(0)) {
        await queryRunner.manager
          .getRepository(CreditHistory)
          .update({ id: membershipUsable.id }, {
            usable: 0,
            lastConsumed: new Date(),
          } as Partial<CreditHistory>);
        await queryRunner.manager.getRepository(MembershipHistory).save({
          invoice: {
            id: invoiceId,
          },
          paid: usable.toNumber(),
          membership: {
            id: membershipUsable.id,
          },
        });
        await this.processMembershipUsable(
          queryRunner,
          credit,
          remain.abs(),
          invoiceId,
        );
      }
      if (remain.greaterThanOrEqualTo(0)) {
        await queryRunner.manager
          .getRepository(CreditHistory)
          .update({ id: membershipUsable.id }, {
            usable: parseFloat(remain.toFixed(2)),
            lastConsumed: new Date(),
          } as Partial<CreditHistory>);
        await queryRunner.manager.getRepository(MembershipHistory).save({
          invoice: {
            id: invoiceId,
          },
          paid: payment.toNumber(),
          membership: {
            id: membershipUsable.id,
          },
        });
      }
      //update
      return membershipUsable;
    }
    return null;
  }

  async processRefundMembershipUsable(
    queryRunner: any,
    credit: any,
    refundPaid: any,
    excludeIds?: any,
  ) {
    const queryBuilder = queryRunner.manager
      .getRepository(CreditHistory)
      .createQueryBuilder('creditHistory')
      .where('creditHistory.credit.id = :creditId', { creditId: credit.id })
      .andWhere('creditHistory.usable >= 0')
      .andWhere('creditHistory.expiryDate IS NOT NULL')
      .andWhere('creditHistory.isRefund = :isRefund', { isRefund: false });
    if (excludeIds && excludeIds.length) {
      queryBuilder.andWhere('creditHistory.id NOT IN (:...excludeIds)', {
        excludeIds,
      });
    }
    queryBuilder.orderBy('creditHistory.expiryDate', 'DESC');
    const membershipUsable = await queryBuilder.getOne();
    if (membershipUsable) {
      const usable = new Decimal(membershipUsable.usable);
      const paid = new Decimal(membershipUsable.paid);
      const usableIds = excludeIds;
      if (usable.equals(paid)) {
        usableIds.push(membershipUsable.id);
        await this.processRefundMembershipUsable(
          queryRunner,
          credit,
          refundPaid,
          usableIds,
        );
      } else {
        const payment = new Decimal(refundPaid);
        const remain = usable.plus(payment);
        if (usable.greaterThan(remain)) {
          const refundRemain = remain.minus(usable);
          await queryRunner.manager
            .getRepository(CreditHistory)
            .update({ id: membershipUsable.id }, {
              usable: parseFloat(usable.toFixed(2)),
            } as Partial<CreditHistory>);
          await this.processRefundMembershipUsable(
            queryRunner,
            credit,
            refundRemain.abs(),
            excludeIds,
          );
        } else {
          await queryRunner.manager
            .getRepository(CreditHistory)
            .update({ id: membershipUsable.id }, {
              usable: parseFloat(remain.toFixed(2)),
            } as Partial<CreditHistory>);
        }
      }
      return membershipUsable;
    }
    return null;
  }

  // Handle update payload when order detail change information. (Using queryRunner)
  async updatePayload(queryRunner, invoice: Invoice) {
    await queryRunner.manager.save(Invoice, {
      id: invoice.id,
      payload: null,
    });
    const payload = await queryRunner.manager.findOne(Invoice, {
      where: { id: invoice.id },
      relations: [
        'branch',
        'referral',
        'appointment',
        'invoiceCoupon',
        'parentInvoice',
        'childInvoices',
        'childInvoices.orders',
        'childInvoices.orders.items',
        'childInvoices.orders.items.employees',
        'childInvoices.orders.items.product',
        'customer',
        'customer.credits',
        'customer.credits.creditSetting',
        'invoicePayments',
        'invoicePayments.paymentMethod',
        'orders',
        'orders.items',
        'orders.items.employees',
        'orders.items.product',
        'orders.items.product.category',
      ],
    });
    await queryRunner.manager.save(Invoice, {
      id: invoice.id,
      payload,
    });
  }

  async roundToNearestHalf(num: Decimal) {
    const decimalNum = new Decimal(num);
    return decimalNum.mul(20).floor().div(20);
  }

  async processValueDiscount(invoice, sumPriceForDiscount) {
    let creditBalance = new Decimal(0);
    const invoicePayments = invoice?.invoicePayments;
    if (invoicePayments) {
      for (const invoicePayment of invoicePayments) {
        if (
          invoicePayment?.paymentMethod?.code === CreditType.NEW ||
          invoicePayment?.paymentMethod?.code === CreditType.OLD
        ) {
          creditBalance = creditBalance.plus(invoicePayment.paid);
        }
      }
    }
    const customer = invoice?.customer;
    if (customer) {
      const credits = await this.repo.manager.find(Credit, {
        where: {
          customer: { id: customer.id },
        },
        relations: ['creditSetting'],
      });
      if (credits?.length) {
        for (const credit of credits) {
          creditBalance = creditBalance.plus(credit.creditBalance);
        }
      }
      if (
        creditBalance.greaterThan(0) &&
        creditBalance.lessThan(sumPriceForDiscount)
      ) {
        sumPriceForDiscount = creditBalance;
      }
    }
    let sumDiscountMoney = new Decimal(0);
    for (const coupon of invoice?.invoiceCoupon) {
      let discountMoney = new Decimal(0);
      let value = new Decimal(0);
      let type;
      // Handle old data of coupon in invoice
      if (coupon.couponType === CouponType.CODE) {
        const whereClause: Record<string, any> = { code: coupon.couponCode };
        const existCouponCode = await this.repo.manager.findOne(CouponItem, {
          where: whereClause,
          relations: [
            'issueCoupon',
            'branches',
            'issueCoupon.coupon',
            'issueCoupon.coupon.status',
            'issueCoupon.coupon.discountType',
            'issueCoupon.status',
          ],
        });
        value = new Decimal(
          existCouponCode?.issueCoupon?.coupon?.discountValue,
        );
        type = existCouponCode?.issueCoupon?.coupon?.discountType?.name;
      }
      if (coupon.couponType === CouponType.MONEY) {
        value = new Decimal(coupon.discountValue);
        type = coupon.couponType;
      }
      if (coupon.couponType === CouponType.PERCENT) {
        value = new Decimal(coupon.percent);
        type = coupon.couponType;
      }
      // End: Handle old data of coupon in invoice
      // Handle new data of coupon when order update new price paid
      if (type && type === CouponType.MONEY) {
        discountMoney = discountMoney.plus(value);
      }
      if (type && type === CouponType.PERCENT) {
        discountMoney = discountMoney.plus(
          sumPriceForDiscount.times(new Decimal(value)).dividedBy(100),
        );
      }
      sumDiscountMoney = sumDiscountMoney.plus(discountMoney);
      coupon.discountValue = parseFloat(discountMoney.toFixed(2));
      // End: Handle new data of coupon when order update new price paid
    }
    return sumDiscountMoney;
  }

  async processValueDiscountForDetail(
    invoiceCoupon,
    invoice,
    sumPriceForDiscount,
  ) {
    let creditBalance = new Decimal(0);
    const invoicePayments = invoice?.invoicePayments;
    if (invoicePayments) {
      for (const invoicePayment of invoicePayments) {
        if (
          invoicePayment?.paymentMethod?.code === CreditType.NEW ||
          invoicePayment?.paymentMethod?.code === CreditType.OLD
        ) {
          creditBalance = creditBalance.plus(invoicePayment.paid);
        }
      }
    }
    const customer = invoice?.customer;
    if (customer) {
      const credits = await this.repo.manager.find(Credit, {
        where: {
          customer: { id: customer.id },
        },
        relations: ['creditSetting'],
      });
      if (credits?.length) {
        for (const credit of credits) {
          creditBalance = creditBalance.plus(credit.creditBalance);
        }
      }
      if (
        creditBalance.greaterThan(0) &&
        creditBalance.lessThan(sumPriceForDiscount)
      ) {
        sumPriceForDiscount = creditBalance;
      }
    }
    let sumDiscountMoney = new Decimal(0);
    for (const coupon of invoiceCoupon) {
      let discountMoney = new Decimal(0);
      let value = new Decimal(0);
      let type;
      // Handle old data of coupon in invoice
      if (coupon.couponType === CouponType.CODE) {
        const whereClause: Record<string, any> = { code: coupon.couponCode };
        const existCouponCode = await this.repo.manager.findOne(CouponItem, {
          where: whereClause,
          relations: [
            'issueCoupon',
            'branches',
            'issueCoupon.coupon',
            'issueCoupon.coupon.status',
            'issueCoupon.coupon.discountType',
            'issueCoupon.status',
          ],
        });
        value = new Decimal(
          existCouponCode?.issueCoupon?.coupon?.discountValue,
        );
        type = existCouponCode?.issueCoupon?.coupon?.discountType?.name;
      }
      if (coupon.couponType === CouponType.MONEY) {
        value = new Decimal(coupon.discountValue);
        type = coupon.couponType;
      }
      if (coupon.couponType === CouponType.PERCENT) {
        value = new Decimal(coupon.percent);
        type = coupon.couponType;
      }
      // End: Handle old data of coupon in invoice
      // Handle new data of coupon when order update new price paid
      if (type && type === CouponType.MONEY) {
        discountMoney = discountMoney.plus(value);
      }
      if (type && type === CouponType.PERCENT) {
        discountMoney = discountMoney.plus(
          sumPriceForDiscount.times(new Decimal(value)).dividedBy(100),
        );
      }
      sumDiscountMoney = sumDiscountMoney.plus(discountMoney);
      coupon.discountValue = parseFloat(discountMoney.toFixed(2));
      // End: Handle new data of coupon when order update new price paid
    }
    return sumDiscountMoney;
  }

  async processInvoiceNewPaidForDetail(
    invoice: any,
    parentInvoice: any,
    childInvoices: any,
  ) {
    // Handle voided invoices - reset newPaid to zero values
    if (invoice.status === InvoiceStatus.VOID) {
      invoice['newPaid'] = {
        totalPaid: 0,
        subTotal: 0,
        totalBeforeTax: 0,
        invoiceCoupon: [],
        isEdited: false,
      };
      return;
    }

    if (invoice.orders.length > 0) {
      invoice.orders.forEach((order) => {
        if (order.payload) {
          const orderPayload: any = order.payload;
          order.items = orderPayload.items;
        }
      });
      if (childInvoices) {
        const newChildInvoices = invoice.childInvoices;
        if (newChildInvoices.length) {
          for (let i = 0; i < newChildInvoices.length; i++) {
            const child = newChildInvoices[i];
            const childInvoiceDetail = await this.repo.findOne({
              where: { id: child.id },
              relations: ['orders', 'appointment'],
            });
            childInvoiceDetail.orders.forEach((order) => {
              if (order.payload) {
                const orderPayload: any = order.payload;
                order.items = orderPayload.items;
              }
            });
            newChildInvoices[i] = childInvoiceDetail;
          }
        }
      }
    }
    if (parentInvoice) {
      invoice.parentInvoice = await this.repo.findOne({
        where: { id: invoice.parentInvoice?.id },
      });
    }
    let sumDiscount = new Decimal(0);
    let sumPriceForDiscount = new Decimal(0);
    let sumSubTotal = new Decimal(0);
    let isEdited = false;
    for (const order of invoice.orders) {
      const orderPayload: any = order.payload;
      for (const productItem of orderPayload.items) {
        const priceItem = !productItem?.couponCode
          ? new Decimal(productItem.quantity).times(
              new Decimal(productItem?.product?.price),
            )
          : new Decimal(0);
        if (!productItem.product.isNotApplyDiscount) {
          sumPriceForDiscount = sumPriceForDiscount.plus(priceItem);
        }
      }
      sumSubTotal = sumSubTotal.plus(new Decimal(order.subTotal));
      if (order.isEdited) {
        isEdited = order.isEdited;
      }
    }
    //copy object invoice.invoiceCoupon
    const newInvoiceCoupon = JSON.parse(JSON.stringify(invoice.invoiceCoupon));
    if (invoice.invoiceCoupon) {
      const discountMoney = await this.processValueDiscountForDetail(
        newInvoiceCoupon,
        invoice,
        sumPriceForDiscount,
      );
      sumDiscount = sumDiscount.plus(new Decimal(discountMoney));
    }
    let totalBeforeTax = sumSubTotal.minus(sumDiscount);
    let totalCreditPaid = new Decimal(0);
    for (const payment of invoice.invoicePayments) {
      if (
        payment.paymentMethod?.code.toLowerCase() === CreditType.NEW ||
        payment.paymentMethod?.code.toLowerCase() === CreditType.OLD
      ) {
        totalBeforeTax = totalBeforeTax.minus(new Decimal(payment.paid));
        totalCreditPaid = totalCreditPaid.plus(new Decimal(payment.paid));
      }
    }
    const newTotalPaid = totalBeforeTax.isPositive()
      ? totalBeforeTax.plus(totalBeforeTax.times(0.09)).plus(totalCreditPaid)
      : new Decimal(0);
    invoice['newPaid'] = {
      totalPaid: parseFloat(newTotalPaid.toFixed(2)),
      subTotal: parseFloat(sumSubTotal.toFixed(2)),
      totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
      invoiceCoupon: newInvoiceCoupon,
      isEdited,
    };
  }

  async updateNoteInvoice(
    invoiceId: string,
    body: { note: string },
  ): Promise<Invoice> {
    const invoice = await this.repo.findOne({ where: { id: invoiceId } });

    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }

    try {
      invoice.note = body.note;

      return await this.repo.save(invoice);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }
}
