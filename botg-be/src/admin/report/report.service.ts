import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest, GetManyDefaultResponse } from 'src/core/crud/crud';
import { Repository } from 'typeorm';
import * as moment from 'moment';
import { UTCtimeZone } from 'src/core/common/common.utils';

import { REPORT_ROUTE } from './report.const';
import { ReporProductService } from './report-product/report-product.service';
import { ProductType } from 'src/core/enums/entity';
import { ReportCustomerService } from './report-customer/report-customer.service';
import { ReportServiceService } from './report-service/report-service.service';
import { ReportSaleService } from './report-sale/report-sale.service';
import { ReportAppointmentService } from './report-appointment/report-appointment.service';
import { ReportCouponService } from './report-coupon/report-coupon.service';
import { ReportEmployeeSaleService } from './report-employee-sale-service/report-employee-sale.service';
import { ReportUserService } from './report-user/report-user.service';
import { ReportDailySaleSummaryService } from './sale/daily-received-summary/daily-received-summary.service';
import { ReportAnalyticsService } from './report-analytics/report-analytics.service';
import * as json2csv from 'json2csv';
import { ReportReturnService } from './report-return/report-return.service';
import { ReportDailyReceivedService } from './sale/daily-received/daily-received.service';
import { ReportSaleSummaryService } from './sale/sale-summary/sale-summary.service';
import { ReportCreditPurchaseService } from './prepaid-credit/credit-purchase/credit-purchase.service';
import { ReportCreditConsumptionService } from './prepaid-credit/credit-consumption/credit-consumption.service';
import { ReportPrepaidConsumptionService } from './prepaid-credit/prepaid-consumption/prepaid-consumption.service';
import { ReportRevenueConversionService } from './sale/revenue-conversion/revenue-conversion.service';
import { ReportSaleDetailService } from './sale/sale-detail/sale-detail.service';
import { ReportFocItemService } from './sale/foc-item/foc-item.service';
import { SerialNumberTrackingService } from './sale/serial-number-tracking/serial-number-tracking.service';

@Injectable()
export class ReportService {
  constructor(
    private readonly reporProductService: ReporProductService,
    private readonly reportCustomerService: ReportCustomerService,
    private readonly reportService: ReportServiceService,
    private readonly reportSale: ReportSaleService,
    private readonly reportAppointment: ReportAppointmentService,
    private readonly reportCouponService: ReportCouponService,
    private readonly reportEmployeeSaleService: ReportEmployeeSaleService,
    private readonly reportUserService: ReportUserService,
    private readonly reportDailySaleSummaryService: ReportDailySaleSummaryService,
    private readonly reportAnalyticsService: ReportAnalyticsService,
    private readonly reportReturnService: ReportReturnService,
    private readonly reportDailyReceivedService: ReportDailyReceivedService,
    private readonly reportSaleSummaryService: ReportSaleSummaryService,
    private readonly reportCreditPurchaseService: ReportCreditPurchaseService,
    private readonly reportCreditConsumptionService: ReportCreditConsumptionService,
    private readonly reportPrepaidConsumptionService: ReportPrepaidConsumptionService,
    private readonly reportRevenueConversionService: ReportRevenueConversionService,
    private readonly reportSaleDetailService: ReportSaleDetailService,
    private readonly reportFocItemService: ReportFocItemService,
    private readonly serialNumberTrackingService: SerialNumberTrackingService,
  ) {}

  async getReport(
    crudReq: CrudRequest,
    req: Request,
    query: any,
    isExport = false,
  ): Promise<GetManyDefaultResponse<any> | any> {
    let result;
    const unwind = json2csv.transforms.unwind;
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    const paymentMethods =
      await this.reportDailyReceivedService.getPaymentMethodList();
    switch (query.name) {
      //Employee Sale & Service
      case REPORT_ROUTE.REPORT_EMPLOYEE_LIST:
        result = await this.reportEmployeeSaleService.getReportEmployeeList(
          crudReq,
          branchIds,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_EMPLOYEE_LIST,
            [
              { label: '#', value: 'order' },
              { label: 'FULL NAME', value: 'fullName' },
              { label: 'DISPLAY NAME', value: 'displayName' },
              { label: 'NRIC/FIN', value: 'nric' },
              { label: 'GENDER', value: 'gender' },
              { label: 'BIRTHDAY', value: 'birthDay' },
              { label: 'PHONE', value: 'phone' },
              { label: 'SALARY TYPE', value: 'salaryType' },
              { label: 'EMPLOYEE CPF(%)', value: 'employeeCPF' },
              { label: 'EMPLOYER CPF(%)', value: 'employerCPF' },
              { label: 'EMAIL', value: 'email' },
              { label: 'ADDRESS', value: 'address' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_NEW_CUSTOMER_SALE:
        result = await this.reportEmployeeSaleService.getReportNewCustomerSale(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          const transforms = [unwind({ paths: ['itemsData'], blankOut: true })];
          return await this.exportReport(
            REPORT_ROUTE.REPORT_NEW_CUSTOMER_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'code' },
              { label: 'CUSTOMER', value: 'customerFullName' },
              { label: 'SALE EMPLOYEE', value: 'employeeName' },
              { label: 'REMARK', value: 'note' },
              { label: 'ITEM', value: 'itemsData.item' },
              {
                label: `PRICE (${query.currencyCode})`,
                value: 'itemsData.price',
              },
              { label: 'QTY', value: 'itemsData.quantity' },
              {
                label: `SALES (${query.currencyCode})`,
                value: 'itemsData.sales',
              },
            ],
            result,
            transforms,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_PREPAID_SERVICE_SALE:
        result =
          await this.reportEmployeeSaleService.getReportPrepaidServiceSale(
            crudReq,
            query,
            isExport,
          );

        if (isExport) {
          const transforms = [unwind({ paths: ['itemsData'], blankOut: true })];
          return await this.exportReport(
            REPORT_ROUTE.REPORT_PREPAID_SERVICE_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'code' },
              { label: 'CUSTOMER', value: 'customerFullName' },
              { label: 'SALE EMPLOYEE', value: 'employeeName' },
              { label: 'SERVICE', value: 'itemsData.item' },
              { label: 'MINS', value: 'itemsData.mins' },
              { label: 'QTY', value: 'itemsData.quantity' },
              {
                label: `GROSS TOTAL (${query.currencyCode})`,
                value: 'itemsData.gross',
              },
              {
                label: `PAID (${query.currencyCode})`,
                value: 'itemsData.paid',
              },
              {
                label: `CHARGE (${query.currencyCode})`,
                value: 'itemsData.charge',
              },
              { label: `NET (${query.currencyCode})`, value: 'itemsData.net' },
            ],
            result,
            transforms,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_EMPLOYEE_PERFORMANCE:
        result =
          await this.reportEmployeeSaleService.getReportEmployeePerformance(
            crudReq,
            query,
            isExport,
          );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_EMPLOYEE_PERFORMANCE,
            [
              { label: '#', value: 'order' },
              { label: 'NAME', value: 'name' },
              { label: `MEMBERSHIP (${query.currencyCode})`, value: 'package' },
              { label: `PRODUCT (${query.currencyCode})`, value: 'product' },
              { label: `SERVICE (${query.currencyCode})`, value: 'service' },
              { label: `COUPON (${query.currencyCode})`, value: 'coupon' },
              { label: `F&B (${query.currencyCode})`, value: 'fAndB' },
              {
                label: `SALE TOTAL (${query.currencyCode})`,
                value: 'saleTotal',
              },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_EMPLOYEE_SERVICE_DETAIL:
        result =
          await this.reportEmployeeSaleService.getReportEmployeeServiceDetail(
            crudReq,
            query,
            isExport,
          );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_EMPLOYEE_SERVICE_DETAIL,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'invoiceCode' },
              { label: 'CUSTOMER', value: 'customerFullName' },
              { label: 'ITEM', value: 'service' },
              { label: 'CATEGORY', value: 'category' },
              { label: 'PREPAID', value: 'prepaid' },
              { label: 'FOC', value: 'foc' },
              { label: 'QTY', value: 'quantity' },
              { label: 'MINS', value: 'duration' },
              { label: `VALUE (${query.currencyCode})`, value: 'value' },
              {
                label: `ACTUAL VALUE (${query.currencyCode})`,
                value: 'actualValue',
              },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_NEW_CUSTOMER_BY_EMPLOYEE:
        result =
          await this.reportEmployeeSaleService.getReportNewCustomerByEmployee(
            crudReq,
            query,
          );
        break;

      case REPORT_ROUTE.REPORT_CUSTOMER_MEMBERSHIP:
        result = await this.reportCustomerService.getReportCustomerMembership(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CUSTOMER_MEMBERSHIP,
            [
              { label: '#', value: 'order' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'NRIC / FIN / PASSPORT', value: 'nric' },
              { label: 'PHONE', value: 'phone' },
              { label: 'MEMBERSHIP NO.', value: 'membershipNo' },
              { label: 'TYPE', value: 'type' },
              { label: 'CREDIT', value: 'credit' },
              { label: 'EXPIRY DATE', value: 'expiryDate' },
              { label: 'LAST VISIT', value: 'lastVisit' },
              { label: 'NEXT APPOINTMENT', value: 'nextAppointment' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_CUSTOMER_LIST:
        result = await this.reportCustomerService.getReportCustomerList(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          const showFields = query.showFields
            ? query.showFields.split(',').map((field) => field.trim())
            : [];

          const allColumns = [
            { label: '#', value: 'order' },
            { label: 'CODE', value: 'code' },
            { label: 'FIRST NAME', value: 'firstName' },
            { label: 'LAST NAME', value: 'lastName' },
            { label: 'NRIC/FIN/PASSPORT', value: 'nric' },
            { label: 'FIRST VISIT', value: 'firstVisit' },
            { label: 'GENDER', value: 'gender' },
            { label: 'NATIONALITY', value: 'nationality' },
            { label: 'MEMBERSHIP NO.', value: 'membershipNo' },
            { label: 'PREFERRED THERAPIST', value: 'preferreds' },
            { label: 'BIRTHDAY', value: 'birthDay' },
            { label: 'PHONE', value: 'phone' },
            { label: 'EMAIL', value: 'email' },
            { label: 'ADDRESS', value: 'address' },
            { label: 'REMARK', value: 'remark' },
          ];

          const filteredColumns = allColumns.filter((col) =>
            showFields.includes(col.value),
          );

          return await this.exportReport(
            REPORT_ROUTE.REPORT_CUSTOMER_LIST,
            filteredColumns,
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_CUSTOMER_SPENDING:
        result = await this.reportCustomerService.getReportCustomerSpending(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CUSTOMER_SPENDING,
            [
              { label: '#', value: 'order' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'NRIC / FIN / PASSPORT', value: 'nric' },
              { label: 'PHONE', value: 'phone' },
              { label: 'MEMBERSHIP NO.', value: 'membershipNo' },
              {
                label: `TOTAL SPENDING (${query.currencyCode})`,
                value: 'totalSpending',
              },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_CUSTOMER_PREPAID_DETAIL:
        result = await this.reportCustomerService.getReportCustomerPrepaid(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CUSTOMER_PREPAID_DETAIL,
            [
              { label: '#', value: 'order' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'PURCHASE DATE', value: 'purchaseDate' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'ITEM', value: 'membershipName' },
              { label: 'BALANCE', value: 'balance' },
              { label: 'VALUE', value: 'value' },
              { label: 'LAST CONSUMED', value: 'lastConsumed' },
              { label: 'EXPIRY DATE', value: 'expiryDate' },
              { label: 'PRICE', value: 'price' },
              { label: 'TOTAL', value: 'paidValue' },
            ],

            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_CREDIT_HISTORY:
        result = await this.reportCustomerService.getReportCreditHistory(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CREDIT_HISTORY,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'created' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'ADJUSTMENT', value: 'adjustment' },
              { label: `AMOUNT`, value: 'amount' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_CUSTOMER_CREDIT:
        const transforms = [unwind({ paths: ['itemsData'], blankOut: true })];
        result = await this.reportCustomerService.getReportCustomerCredit(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CUSTOMER_CREDIT,
            [
              { label: '#', value: 'order' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'PACKAGE', value: 'membershipName' },
              { label: 'TYPE', value: 'type' },
              { label: 'BALANCE', value: 'balance' },
              { label: `TOTAL`, value: 'total' },
              { label: 'EXPIRY DATE', value: 'expiryDate' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_OPENING_CLOSING_CREDIT:
        result = await this.reportCustomerService.getReportOpeningClosingCredit(
          crudReq,
          query,
          isExport,
        );

        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_OPENING_CLOSING_CREDIT,
            [
              { label: ' ', value: 'title' },
              { label: 'QUANITY', value: 'opening' },
              { label: ' ', value: 'purchase' },
              { label: ' ', value: 'use' },
              { label: ' ', value: 'expired' },
              { label: ' ', value: 'closing' },
              { label: ' ', value: 'type' },
              { label: 'VALUE', value: 'value_opening' },
              { label: ' ', value: 'value_purchase' },
              { label: ' ', value: 'value_use' },
              { label: ' ', value: 'value_expired' },
              { label: ' ', value: 'value_closing' },
              { label: ' ', value: 'value_expiryDate' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_TREATMENT_HISTORY:
        result = await this.reportCustomerService.getReportTreatmentHistory(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_TREATMENT_HISTORY,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'TREATED BY', value: 'treatedBy' },
              { label: 'ITEM', value: 'adjusment' },
              { label: 'QTY', value: 'qty' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_NEW_CUSTOMER:
        result = await this.reportCustomerService.getReportNewCustomer(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          const transforms = [unwind({ paths: ['itemsData'], blankOut: true })];
          return await this.exportReport(
            REPORT_ROUTE.REPORT_NEW_CUSTOMER,
            [
              { label: '#', value: 'order' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'REFERENCE NO.', value: 'itemsData.referenceNo' },
              {
                label: `PRODUCT (${query.currencyCode})`,
                value: 'productSummary',
              },
              {
                label: `SERVICE (${query.currencyCode})`,
                value: 'serviceSummary',
              },
              {
                label: `COUPON (${query.currencyCode})`,
                value: 'couponSummary',
              },
              {
                label: `MEMBERSHIP (${query.currencyCode})`,
                value: 'membershipSummary',
              },
              {
                label: `F&B (${query.currencyCode})`,
                value: 'foodBeverageSummary',
              },
              {
                label: `DEDUCTION (${query.currencyCode})`,
                value: 'deduction',
              },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `PAYMENT (${query.currencyCode})`, value: 'payment' },
            ],
            result,
            transforms,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_PRODUCT:
        result = await this.reporProductService.getReport(
          crudReq,
          query,
          ProductType.PRODUCT,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_PRODUCT,
            [
              { label: '#', value: 'order' },
              { label: 'PRODUCT NAME', value: 'name' },
              { label: 'DETAIL', value: 'category' },
              { label: ' ', value: 'isMember' },
              { label: ' ', value: 'cost' },
              { label: ' ', value: 'price' },
              { label: 'COMMISSION', value: 'commission.employee' },
              { label: ' ', value: 'commission.frondesk' },
              { label: ' ', value: 'commission.staff' },
            ],
            result.data,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_SERVICE:
        result = await this.reportService.getReportService(
          crudReq,
          query,
          ProductType.SERVICE,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SERVICE,
            [
              { label: '#', value: 'order' },
              { label: 'SERVICE NAME', value: 'name' },
              { label: 'DETAIL', value: 'section' },
              { label: ' ', value: 'category' },
              { label: ' ', value: 'isMember' },
              { label: ' ', value: 'time' },
              { label: ' ', value: 'cost' },
              { label: ' ', value: 'price' },
              { label: 'COMMISSION', value: 'commission.employee' },
              { label: ' ', value: 'commission.frondesk' },
              { label: ' ', value: 'commission.staff' },
            ],
            result.data,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_MEMBERSHIP:
        result = await this.reportService.getReportMembership(
          crudReq,
          query,
          ProductType.MEMBERSHIP,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_MEMBERSHIP,
            [
              { label: '#', value: 'order' },
              { label: 'MEMBERSHIP NAME', value: 'name' },
              { label: 'DETAIL', value: 'category' },
              { label: ' ', value: 'time' },
              { label: ' ', value: 'credit' },
              { label: ' ', value: 'price' },
              { label: 'COMMISSION', value: 'commission.employee' },
              { label: ' ', value: 'commission.frondesk' },
              { label: ' ', value: 'commission.staff' },
            ],
            result.data,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_COUNPON:
        result = await this.reportCouponService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_COUNPON,
            [
              { label: '#', value: 'order' },
              { label: 'COUPON NAME', value: 'name' },
              { label: ' ', value: 'category' },
              { label: ' ', value: 'time' },
              { label: 'DETAIL', value: 'price' },
              { label: ' ', value: 'discountValue' },
              {
                label: ' ',
                value: 'discountType.name',
              },
            ],
            result.data,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_DAILY_RECEIVED:
        result = await this.reportDailyReceivedService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_DAILY_RECEIVED,
            [
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'formatCustomer' },
              { label: 'SALE EMPLOYEE', value: 'saleEmployee' },
              {
                label: `SALE TOTAL (${query.currencyCode})`,
                value: 'saleTotal',
              },
              { label: `RECEIVED (${query.currencyCode})`, value: 'received' },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              {
                label: `TAX RECEIVED (${query.currencyCode})`,
                value: 'taxReceived',
              },
              { label: `CHARGE (${query.currencyCode})`, value: 'charge' },
              {
                label: `NET RECEIVED (${query.currencyCode})`,
                value: 'netReceived',
              },
              { label: `CASH (${query.currencyCode})`, value: 'cash' },
              { label: `CARD (${query.currencyCode})`, value: 'card' },
              { label: `OTHERS (${query.currencyCode})`, value: 'others' },
              { label: `CREDIT (${query.currencyCode})`, value: 'credit' },
              { label: `DUE (${query.currencyCode})`, value: 'due' },
              { label: '', value: '' },

              ...paymentMethods.map((method) => ({
                label: `${method.name} (${query.currencyCode})`,
                value: method.code,
              })),
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_Z_REPORT:
        result = await this.reportSale.getReportZ(crudReq, query);
        break;
      case REPORT_ROUTE.REPORT_PAYMENT:
        result = await this.reportSale.getReportPayment(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_PAYMENT,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'PAYMENT DETAILS', value: 'note' },
              { label: 'PAYMENT METHOD', value: 'payment.method.name' },
              { label: `TOTAL (${query.currencyCode}) (A)`, value: 'total' },
              { label: `CHARGE (${query.currencyCode}) (B)`, value: 'charge' },
              {
                label: `(${query.currencyCode}) (A) - (B)`,
                value: 'difference',
              },
            ],
            result.data,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_SALE_TAX:
        result = await this.reportSale.getReportSaleTax(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SALE_TAX,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `GST 9% (${query.currencyCode})`, value: 'tax' },
              { label: `NET (${query.currencyCode})`, value: 'net' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_TAX_SUMMARY:
        result = await this.reportSale.getReportTaxSummary(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_TAX_SUMMARY,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `GST 9% (${query.currencyCode})`, value: 'tax' },
              { label: `NET (${query.currencyCode})`, value: 'net' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_DAILY_RECEIVED_SUMMARY:
        result = await this.reportDailySaleSummaryService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_DAILY_RECEIVED_SUMMARY,
            [
              { label: 'DATE', value: 'date' },
              { label: 'TOTAL INVOICES', value: 'total_invoice' },
              {
                label: `SALE TOTAL (${query.currencyCode})`,
                value: 'saleTotal',
              },
              {
                label: `PAYMENT TOTAL (${query.currencyCode})`,
                value: 'paymentTotal',
              },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              {
                label: `TAX RECEIVED (${query.currencyCode})`,
                value: 'taxReceived',
              },
              { label: `CHARGE (${query.currencyCode})`, value: 'charge' },
              {
                label: `NET RECEIVED (${query.currencyCode})`,
                value: 'netReceived',
              },
              { label: `RECEIVED (${query.currencyCode})`, value: 'received' },
              { label: `CASH (${query.currencyCode})`, value: 'cash' },
              { label: `CARD (${query.currencyCode})`, value: 'card' },
              { label: `OTHERS (${query.currencyCode})`, value: 'others' },
              { label: `CREDIT (${query.currencyCode})`, value: 'credit' },
              { label: `DUE (${query.currencyCode})`, value: 'due' },
              { label: '', value: '' },
              ...paymentMethods.map((method) => ({
                label: `${method.name} (${query.currencyCode})`,
                value: method.code,
              })),
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_SALE_SUMMARY:
        result = await this.reportSaleSummaryService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SALE_SUMMARY,
            [
              { label: 'DATE', value: 'formatted_date' },
              {
                label: `SALE TOTAL (${query.currencyCode})`,
                value: 'saleTotal',
              },
              {
                label: `PAYMENT TOTAL (${query.currencyCode})`,
                value: 'paymentTotal',
              },
              { label: `RECEIVED (${query.currencyCode})`, value: 'received' },
              { label: `CHARGE (${query.currencyCode})`, value: 'charge' },
              {
                label: `NET RECEIVED (${query.currencyCode})`,
                value: 'netReceived',
              },
              { label: `CASH (${query.currencyCode})`, value: 'cash' },
              { label: `CARD (${query.currencyCode})`, value: 'card' },
              { label: `OTHERS (${query.currencyCode})`, value: 'others' },
              { label: `FOC/WAIVED (${query.currencyCode})`, value: 'foc' },
              { label: `CREDIT (${query.currencyCode})`, value: 'credit' },
              { label: `DUE (${query.currencyCode})`, value: 'due' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_FOOD_AND_BEVERAGE_SALE:
        result = await this.reportSale.getReportFoodAndBeverageSale(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_FOOD_AND_BEVERAGE_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customer' },
              { label: 'ITEMS', value: 'items' },
              { label: 'TYPE', value: 'type' },
              { label: 'CATEGORY', value: 'category' },
              { label: 'QTY', value: 'qty' },
              { label: 'DISCOUNT', value: 'discount' },
              { label: 'TOTAL', value: 'total' },
              { label: 'TAX', value: 'tax' },
              { label: 'PAYMENT METHOD', value: 'paymentMethod' },
              { label: 'SALE EMPLOYEE', value: 'saleEmployee' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_SALE_PROFIT:
        result = await this.reportSale.getReportSaleProfit(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SALE_PROFIT,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'TAX', value: 'tax' },
              { label: 'GROSS SALES', value: 'grossSales' },
              { label: 'REFUND', value: 'refund' },
              { label: 'DISCOUNT', value: 'discount' },
              { label: 'PAYMENT CHARGE', value: 'paymentCharge' },
              { label: 'NET SALES', value: 'netSales' },
              { label: 'COST', value: 'cost' },
              { label: 'PROFIT', value: 'profit' },
              { label: 'SALES MARGIN (%)', value: 'salesMargin' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_REVENUE_CONVERSION:
        result = await this.reportRevenueConversionService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_REVENUE_CONVERSION,
            [
              { label: 'DATE', value: 'date' },
              {
                label: `TOTAL SALE (A) (${query.currencyCode})`,
                value: 'totalSale',
              },
              {
                label: `PREPAID SALE (B) (${query.currencyCode})`,
                value: 'prepaidSale',
              },
              {
                label: `OTHER SALE (A) - (B) (${query.currencyCode})`,
                value: 'otherSale',
              },
              {
                label: `PREPAID CONSUMPTION (C) (${query.currencyCode})`,
                value: 'prepaidConsumption',
              },
              {
                label: `EXPIRED PREPAID (D) (${query.currencyCode})`,
                value: 'expiredPrepaid',
              },
              {
                label: `DISCOUNT (E) (${query.currencyCode})`,
                value: 'discount',
              },
              {
                label: `NET REVENUE (A) - (B) + (C) + (D) - (E) (${query.currencyCode})`,
                value: 'netRevenue',
              },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_SALE_DETAIL:
        result = await this.reportSaleDetailService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SALE_DETAIL,
            [
              { label: '#', value: 'lineNumber' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customer' },
              { label: 'EMPLOYEE', value: 'employee' },
              { label: 'ITEM', value: 'item' },
              { label: 'TYPE', value: 'type' },
              { label: `PRICE (${query.currencyCode})`, value: 'price' },
              { label: 'QTY', value: 'qty' },
              { label: `DISCOUNT (${query.currencyCode})`, value: 'discount' },
              { label: `CREDIT (${query.currencyCode})`, value: 'credit' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: 'PREPAID', value: 'prepaid' },
              { label: 'FOC', value: 'foc' },
              { label: `SUBTOTAL (${query.currencyCode})`, value: 'subtotal' },
              {
                label: `DISCOUNT (${query.currencyCode})`,
                value: 'discountAmount',
              },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              { label: `TOTAL (${query.currencyCode})`, value: 'totalAmount' },
              { label: 'RECEIVED', value: 'received' },
              { label: 'PAYMENT METHOD', value: 'paymentMethod' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_SERVICE_DAILY_SALE:
        result = await this.reportSale.getReportServiceDailySale(
          crudReq,
          query,
          ProductType.SERVICE,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SERVICE_DAILY_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'PREPAID', value: 'prepaid' },
              { label: 'SERVICE', value: 'serviceName' },
              { label: 'CATEGORY', value: 'categoryName' },
              { label: 'MINS', value: 'mins' },
              { label: 'QTY', value: 'quantity' },
              { label: `DISCOUNT (${query.currencyCode})`, value: 'discount' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              { label: 'PAYMENT METHOD', value: 'paymentMethod' },
              { label: 'SALE EMPLOYEE', value: 'employee' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_ALA_CARTE_DAILY_SALE:
        result = await this.reportSale.getReportAlaCarteServiceDailySale(
          crudReq,
          query,
          ProductType.SERVICE,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_ALA_CARTE_DAILY_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'SERVICE', value: 'serviceName' },
              { label: 'SALE EMPLOYEE', value: 'saleEmployee' },
              { label: 'SERVICE THERAPIST', value: 'employee' },
              { label: 'CATEGORY', value: 'categoryName' },
              { label: 'MINS', value: 'mins' },
              { label: `VALUE (${query.currencyCode})`, value: 'value' },
              { label: 'QTY', value: 'quantity' },
              { label: `SALE (${query.currencyCode})`, value: 'total' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_MEMBERSHIP_DAILY_SALE:
        result = await this.reportSale.getReportMembershipDailySale(
          crudReq,
          query,
          ProductType.MEMBERSHIP,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_MEMBERSHIP_DAILY_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'MEMBERSHIP', value: 'membershipName' },
              { label: 'CATEGORY', value: 'categoryName' },
              { label: 'QTY', value: 'quantity' },
              { label: `DISCOUNT (${query.currencyCode})`, value: 'discount' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              { label: 'PAYMENT METHOD', value: 'paymentMethod' },
              { label: 'SALE EMPLOYEE', value: 'employee' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_COUPON_DAILY_SALE:
        result = await this.reportSale.getReportCouponDailySale(
          crudReq,
          query,
          ProductType.COUPON,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_COUPON_DAILY_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'PREPAIRD', value: 'prepaid' },
              { label: 'COUPON', value: 'couponName' },
              { label: 'CATEGORY', value: 'categoryName' },
              { label: 'QTY', value: 'quantity' },
              { label: `DISCOUNT (${query.currencyCode})`, value: 'discount' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              { label: 'PAYMENT METHOD', value: 'paymentMethod' },
              { label: 'SALE EMPLOYEE', value: 'employee' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_PRODUCT_DAILY_SALE:
        result = await this.reportSale.getReportProductDailySale(
          crudReq,
          query,
          ProductType.PRODUCT,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_PRODUCT_DAILY_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customerInfo' },
              { label: 'PRODUCT', value: 'productName' },
              { label: 'CATEGORY', value: 'categoryName' },
              { label: 'QTY', value: 'quantity' },
              { label: `DISCOUNT (${query.currencyCode})`, value: 'discount' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: `TAX (${query.currencyCode})`, value: 'tax' },
              { label: 'PAYMENT METHOD', value: 'paymentMethod' },
              { label: 'SALE EMPLOYEE', value: 'employee' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_DAILY_SALES:
        result = await this.reportSale.getExportDailySale(
          crudReq,
          branchIds,
          query.currencyCode,
        );
        return await this.exportReport(
          REPORT_ROUTE.REPORT_DAILY_SALES,
          [
            { label: ' ', value: 'title' },
            { label: ' ', value: 'value' },
          ],
          result,
        );
        break;

      case REPORT_ROUTE.REPORT_AUDIT_TRAIL:
        result = await this.reportAnalyticsService.getReportAuditTrail(
          crudReq,
          query,
          isExport,
          query.clientZoneName || 'Asia/Ho_Chi_Minh',
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_AUDIT_TRAIL,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'OPERATION DATE', value: 'operationDate' },
              { label: 'USER', value: 'user' },
              { label: 'OPERATION', value: 'operation' },
              { label: 'NOTE', value: 'note' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_RETURNED_ITEM:
        result = await this.reportReturnService.getReportReturnedItem(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_RETURNED_ITEM,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'formatDate' },
              { label: 'PURCHASE REFERENCE NO.', value: 'formatReferenceNo' },
              { label: 'PURCHASE DATE', value: 'formatPurchaseDate' },
              { label: 'CUSTOMER', value: 'customer.formatName' },
              { label: 'SALE EMPLOYEE', value: 'employee.sale.name' },
              { label: 'ITEM', value: 'product.name' },
              { label: `RETURNED`, value: 'returned' },
              { label: `VALUE (${query.currencyCode})`, value: 'value' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_VOIDED_SALE:
        result = await this.reportAnalyticsService.getReportVoidedSale(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          const transforms = [unwind({ paths: ['itemsData'], blankOut: true })];
          return await this.exportReport(
            REPORT_ROUTE.REPORT_VOIDED_SALE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'formatDate' },
              { label: 'REFERENCE NO.', value: 'formatReferenceNo' },
              { label: 'CUSTOMER', value: 'customer.formatName' },
              { label: 'SALE EMPLOYEE', value: 'employee.sale.name' },
              { label: 'ITEM', value: 'itemsData.item.name' },
              { label: 'QTY', value: 'itemsData.quantity' },
              { label: `TOTAL (${query.currencyCode})`, value: 'total' },
              { label: 'REAMARK', value: 'reamark' },
              { label: 'VOIDED ON', value: 'formatVoidDate' },
            ],
            result,
            transforms,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_SALE_RANKING:
        result = await this.reportAnalyticsService.getReportSaleRanking(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SALE_RANKING,
            [
              { label: ' ', value: 'order' },
              { label: ' ', value: 'item' },
              { label: ' ', value: 'type' },
              { label: ' ', value: 'category' },
              { label: 'PREPAID SALE', value: 'prepaidQuantity' },
              { label: ' ', value: 'prepaidTotal' },
              { label: 'TOTAL SALE', value: 'quantity' },
              { label: ' ', value: 'total' },
              { label: ' ', value: 'cost' },
              { label: ' ', value: 'profit' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_APPOINTMENT:
        result = await this.reportAppointment.getReportAppointment(
          crudReq,
          query,
          ProductType.SERVICE,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_APPOINTMENT,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'formatDate' },
              { label: 'CUSTOMER', value: 'customer.formatName' },
              { label: 'PHONE', value: 'customer.phone' },
              { label: 'THERAPIST', value: 'employee.formatService' },
              { label: 'SERVICE', value: 'product.name' },
              { label: 'MINS', value: 'product.duration.name' },
              { label: 'APPOINTMENT STATUS', value: 'formatStatus' },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_APPOINTMENT_NO_SHOW:
        result = await this.reportAppointment.getReportAppointmentNoShow(
          crudReq,
          query,
          ProductType.SERVICE,
        );
        break;
      case REPORT_ROUTE.REPORT_APPOINTMENT_CHECK_IN_OUT:
        result = await this.reportAppointment.getReportAppointmentCheckInOut(
          crudReq,
          query,
          ProductType.SERVICE,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_APPOINTMENT_CHECK_IN_OUT,
            [
              { label: '#', value: 'order' },
              { label: 'APPOINTMENT DATE', value: 'dateFormated' },
              { label: 'REFERENCE NO.', value: 'referenceNoFormated' },
              { label: 'CUSTOMER', value: 'customer.formatName' },
              { label: 'PHONE', value: 'customer.phone' },
              { label: 'NRIC', value: 'customer.nric' },
              { label: 'NATIONALITY', value: 'customer.nationality.name' },
              { label: 'CHECK IN', value: 'checkInFormated' },
              { label: 'CHECK OUT', value: 'checkOutFormated' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_SERVICE_PERFORM:
        result = await this.reportService.getReportServicePerform(
          crudReq,
          query,
          ProductType.SERVICE,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SERVICE_PERFORM,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'formatDate' },
              { label: 'REFERENCE NO.', value: 'formatReferenceNo' },
              { label: 'CUSTOMER', value: 'customer.formatName' },
              { label: 'TREATED BY', value: 'treatedBy' },
              { label: 'ITEM', value: 'product.name' },
              { label: 'QUANTITY', value: 'quantity' },
              { label: `VALUE (${query.currencyCode})`, value: 'value' },
              {
                label: `ACTUAL VALUE (${query.currencyCode})`,
                value: 'actualValue',
              },
            ],
            result,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_USER_LOGON:
        result = await this.reportUserService.getReportUserLogon(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_USER_LOGON,
            [
              { label: '#', value: 'order' },
              { label: 'USERNAME', value: 'user.username' },
              { label: 'ROLE', value: 'role.name' },
              { label: 'LOGIN', value: 'login' },
              { label: 'LOGOUT', value: 'logout' },
            ],
            result,
          );
        }
        break;
      // Prepaid & Credit
      case REPORT_ROUTE.REPORT_CREDIT_PURCHASE:
        result = await this.reportCreditPurchaseService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CREDIT_PURCHASE,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER.', value: 'customer' },
              { label: 'MEMBERSHIP', value: 'membership' },
              { label: 'TYPE', value: 'type' },
              {
                label: `SELLING PRICE (${query.currencyCode})`,
                value: 'sellingPrice',
              },
              { label: 'CREDIT QTY', value: 'creditQuantity' },
              { label: 'CREDIT TOTAL', value: 'creditTotal' },
              { label: 'CREDIT BEFORE', value: 'creditBefore' },
              { label: 'TOTAL USAGE', value: 'totalUsage' },
              { label: 'BALANCE', value: 'balance' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_CREDIT_CONSUMPTION:
        result = await this.reportCreditConsumptionService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          const transforms = [unwind({ paths: ['items'], blankOut: true })];
          return await this.exportReport(
            REPORT_ROUTE.REPORT_CREDIT_CONSUMPTION,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER.', value: 'customer' },
              { label: 'CREDIT', value: 'membershipName' },
              { label: 'ACQUIRED DATE', value: 'acquiredDate' },
              { label: 'ACQUIRED REFERENCE NO.', value: 'acquiredReferenceNo' },
              { label: 'TYPE', value: 'type' },
              { label: 'BEFORE', value: 'before' },
              { label: 'ITEM', value: 'items.name' },
              { label: 'QTY', value: 'items.quantity' },
              { label: 'CONSUMED', value: 'items.consumed' },
              { label: `VALUE (${query.currencyCode})`, value: 'value' },
              { label: 'REMAIN', value: 'remain' },
            ],
            result,
            transforms,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_PREPAID_CONSUMPTION:
        result = await this.reportPrepaidConsumptionService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          const transforms = [unwind({ paths: ['items'], blankOut: true })];
          return await this.exportReport(
            REPORT_ROUTE.REPORT_PREPAID_CONSUMPTION,
            [
              { label: '#', value: 'order' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER.', value: 'customer' },
              { label: 'CATEGORY', value: 'category' },
              { label: 'ITEM', value: 'item' },
              { label: 'PURCHASE DATE', value: 'purchaseDate' },
              { label: 'PURCHASE REFERENCE NO.', value: 'purchaseReferenceNo' },
              { label: 'UNIT PRICE', value: 'unitPrice' },
              { label: 'CONSUMED', value: 'consumed' },
              { label: 'VALUE', value: 'value' },
              { label: 'REMAIN', value: 'remain' },
              { label: 'USABLE', value: 'usable' },
              { label: 'PURCHASED AT', value: 'purchasedAt' },
            ],
            result,
            transforms,
          );
        }
        break;
      case REPORT_ROUTE.REPORT_FOC_ITEM:
        result = await this.reportFocItemService.getReport(
          crudReq,
          query,
          isExport,
        );
        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_FOC_ITEM,
            [
              { label: '#', value: 'lineNumber' },
              { label: 'DATE', value: 'date' },
              { label: 'REFERENCE NO.', value: 'referenceNo' },
              { label: 'CUSTOMER', value: 'customer' },
              { label: 'EMPLOYEE', value: 'employee' },
              { label: 'ITEM', value: 'item' },
              { label: 'QTY', value: 'qty' },
              { label: `VALUE (${query.currencyCode})`, value: 'value' },
            ],
            result,
          );
        }
        break;

      case REPORT_ROUTE.REPORT_SERIAL_NUMBER_TRACKING:
        const {
          purchasedStartDate,
          purchasedEndDate,
          redeemedStartDate,
          redeemedEndDate,
        } = query;
        // Parse dates with specific format and convert to ISO string
        const formattedStartDate = purchasedStartDate
          ? moment(purchasedStartDate, 'YYYY-MM-DD HH:mm:ss.SSS ZZ')
              .tz(query.clientZoneName || 'Asia/Ho_Chi_Minh')
              .tz(UTCtimeZone)
              .toISOString()
          : null;

        const formattedEndDate = purchasedEndDate
          ? moment(purchasedEndDate, 'YYYY-MM-DD HH:mm:ss.SSS ZZ')
              .tz(query.clientZoneName || 'Asia/Ho_Chi_Minh')
              .tz(UTCtimeZone)
              .toISOString()
          : null;

        const formattedRedeemStartDate = redeemedStartDate
          ? moment(redeemedStartDate, 'YYYY-MM-DD HH:mm:ss.SSS ZZ')
              .tz(query.clientZoneName || 'Asia/Ho_Chi_Minh')
              .tz(UTCtimeZone)
              .toISOString()
          : null;

        const formattedRedeemEndDate = redeemedEndDate
          ? moment(redeemedEndDate, 'YYYY-MM-DD HH:mm:ss.SSS ZZ')
              .tz(query.clientZoneName || 'Asia/Ho_Chi_Minh')
              .tz(UTCtimeZone)
              .toISOString()
          : null;

        result = await this.serialNumberTrackingService.getReport(
          crudReq,
          {
            purchaseStartDate: formattedStartDate,
            purchaseEndDate: formattedEndDate,
            redeemedStartDate: formattedRedeemStartDate,
            redeemedEndDate: formattedRedeemEndDate,
            branchIds,
            clientZoneName: query.clientZoneName,
            searchText: query.searchText,
          },
          isExport,
        );

        if (isExport) {
          return await this.exportReport(
            REPORT_ROUTE.REPORT_SERIAL_NUMBER_TRACKING,
            [
              { label: '#', value: 'lineNumber' },
              { label: 'COUPON', value: 'coupon' },
              { label: 'SERIAL NUMBER', value: 'serialNumber' },
              { label: 'SOLD DATE', value: 'soldDate' },
              { label: 'SOLD CUSTOMER', value: 'soldCustomer' },
              { label: 'SOLD REFERENCE CODE', value: 'soldReferenceCode' },
              { label: 'USED DATE', value: 'usedDate' },
              { label: 'USED CUSTOMER', value: 'usedCustomer' },
              { label: 'USED REFERENCE CODE', value: 'usedReferenceCode' },
            ],
            result,
          );
        }
        break;
    }

    return result;
  }

  async exportReport(
    name: string,
    fields: { label: string; value: string }[],
    data: any,
    transforms?: any,
  ) {
    const csv = await json2csv.parse(data, {
      fields: fields,
      transforms: transforms,
    });

    const timeStamp = Date.now();
    const filename = `${name}-${timeStamp}.csv`;
    return { filename, csv };
  }
}
